#!/usr/bin/env node

/**
 * API Endpoint Discovery Script for Fr8+ Backend
 * 
 * This script discovers the correct API endpoints by testing various combinations
 * of endpoint paths, methods, and headers to find what the API server actually accepts.
 */

const BASE_URL = 'http://localhost:4030';

// Test organization ID and PIN from the conversation
const TEST_ORG_ID = '1';
const TEST_ORG_PIN = '1234';

/**
 * Test an API endpoint with various configurations
 */
async function testEndpoint(path, method = 'GET', headers = {}, body = null) {
  const url = `${BASE_URL}${path}`;
  
  const config = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  };
  
  if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    config.body = JSON.stringify(body);
  }
  
  try {
    console.log(`\n🔍 Testing: ${method} ${url}`);
    console.log(`📨 Headers:`, Object.keys(config.headers).join(', '));
    
    const response = await fetch(url, config);
    const contentType = response.headers.get('content-type');
    
    let responseData = null;
    try {
      if (contentType && contentType.includes('application/json')) {
        responseData = await response.json();
      } else {
        responseData = await response.text();
      }
    } catch (e) {
      responseData = 'Unable to parse response';
    }
    
    console.log(`📥 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      console.log(`✅ SUCCESS`);
      if (responseData) {
        console.log(`📊 Response:`, typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData);
      }
    } else {
      console.log(`❌ FAILED`);
      if (responseData) {
        console.log(`🚨 Error:`, typeof responseData === 'object' ? JSON.stringify(responseData, null, 2) : responseData);
      }
    }
    
    return {
      url,
      method,
      status: response.status,
      success: response.ok,
      headers: Object.fromEntries(response.headers.entries()),
      data: responseData
    };
  } catch (error) {
    console.log(`💥 Network Error: ${error.message}`);
    return {
      url,
      method,
      status: 0,
      success: false,
      error: error.message
    };
  }
}

/**
 * Main discovery function
 */
async function discoverEndpoints() {
  console.log('🔍 FR8+ API ENDPOINT DISCOVERY');
  console.log('===============================');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Test Org ID: ${TEST_ORG_ID}`);
  console.log(`Test Org PIN: ${TEST_ORG_PIN.substring(0, 2)}***`);
  
  const results = [];
  
  // 1. Test health endpoint
  console.log('\n📋 TESTING HEALTH ENDPOINTS');
  const healthEndpoints = ['/health', '/api/v1/health', '/api/v1/health/', '/status'];
  for (const endpoint of healthEndpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
  }
  
  // 2. Test organization endpoints (public - no auth required)
  console.log('\n📋 TESTING ORGANIZATION ENDPOINTS (PUBLIC)');
  const orgEndpoints = [
    '/organization',
    '/organization/',
    '/api/v1/organization',
    '/api/v1/organization/',
    '/orgs',
    '/orgs/',
    '/api/v1/orgs',
    '/api/v1/orgs/'
  ];
  
  for (const endpoint of orgEndpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
  }
  
  // 3. Test organization endpoints with auth headers
  console.log('\n📋 TESTING ORGANIZATION ENDPOINTS (WITH AUTH)');
  const authHeaders = {
    'X-Organization-ID': TEST_ORG_ID,
    'X-Organization-PIN': TEST_ORG_PIN
  };
  
  for (const endpoint of orgEndpoints) {
    const result = await testEndpoint(endpoint, 'GET', authHeaders);
    results.push(result);
  }
  
  // 4. Test shipment endpoints
  console.log('\n📋 TESTING SHIPMENT ENDPOINTS');
  const shipmentEndpoints = [
    '/shipment',
    '/shipment/',
    '/api/v1/shipment',
    '/api/v1/shipment/',
    '/shipments',
    '/shipments/',
    '/api/v1/shipments',
    '/api/v1/shipments/',
    '/tracking',
    '/tracking/',
    '/api/v1/tracking',
    '/api/v1/tracking/',
    '/status/all',
    '/api/v1/status/all'
  ];
  
  for (const endpoint of shipmentEndpoints) {
    // Test without auth
    const result1 = await testEndpoint(endpoint);
    results.push(result1);
    
    // Test with org ID only
    const result2 = await testEndpoint(endpoint, 'GET', { 'X-Organization-ID': TEST_ORG_ID });
    results.push(result2);
    
    // Test with both org ID and PIN
    const result3 = await testEndpoint(endpoint, 'GET', authHeaders);
    results.push(result3);
  }
  
  // 5. Test user endpoints
  console.log('\n📋 TESTING USER ENDPOINTS');
  const userEndpoints = [
    '/user',
    '/user/',
    '/api/v1/user',
    '/api/v1/user/',
    '/users',
    '/users/',
    '/api/v1/users',
    '/api/v1/users/'
  ];
  
  for (const endpoint of userEndpoints) {
    // Test without auth
    const result1 = await testEndpoint(endpoint);
    results.push(result1);
    
    // Test with auth headers
    const result2 = await testEndpoint(endpoint, 'GET', authHeaders);
    results.push(result2);
  }
  
  // 6. Test API documentation/info endpoints
  console.log('\n📋 TESTING API INFO ENDPOINTS');
  const infoEndpoints = [
    '/',
    '/api',
    '/api/',
    '/api/v1',
    '/api/v1/',
    '/docs',
    '/docs/',
    '/api/docs',
    '/api/docs/',
    '/api/v1/docs',
    '/api/v1/docs/',
    '/swagger',
    '/openapi.json'
  ];
  
  for (const endpoint of infoEndpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
  }
  
  // 7. Analyze results
  console.log('\n📊 DISCOVERY RESULTS SUMMARY');
  console.log('============================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful requests: ${successful.length}`);
  console.log(`❌ Failed requests: ${failed.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ WORKING ENDPOINTS:');
    successful.forEach(result => {
      console.log(`  ${result.method} ${result.url} → ${result.status}`);
    });
  }
  
  // Look for specific endpoint patterns
  const orgEndpointsWorking = successful.filter(r => r.url.includes('organization') || r.url.includes('org'));
  const shipmentEndpointsWorking = successful.filter(r => 
    r.url.includes('shipment') || r.url.includes('tracking') || r.url.includes('status')
  );
  const userEndpointsWorking = successful.filter(r => r.url.includes('user'));
  
  if (orgEndpointsWorking.length > 0) {
    console.log('\n🏢 ORGANIZATION ENDPOINTS:');
    orgEndpointsWorking.forEach(result => {
      console.log(`  ${result.method} ${result.url} → ${result.status}`);
    });
  }
  
  if (shipmentEndpointsWorking.length > 0) {
    console.log('\n📦 SHIPMENT/TRACKING ENDPOINTS:');
    shipmentEndpointsWorking.forEach(result => {
      console.log(`  ${result.method} ${result.url} → ${result.status}`);
    });
  }
  
  if (userEndpointsWorking.length > 0) {
    console.log('\n👥 USER ENDPOINTS:');
    userEndpointsWorking.forEach(result => {
      console.log(`  ${result.method} ${result.url} → ${result.status}`);
    });
  }
  
  // Check for different status codes
  const statusCodes = {};
  results.forEach(result => {
    statusCodes[result.status] = (statusCodes[result.status] || 0) + 1;
  });
  
  console.log('\n📊 STATUS CODE BREAKDOWN:');
  Object.entries(statusCodes).forEach(([code, count]) => {
    console.log(`  ${code}: ${count} occurrences`);
  });
  
  // Save detailed results to file
  const fs = require('fs');
  const detailedResults = {
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL,
    testOrgId: TEST_ORG_ID,
    summary: {
      total: results.length,
      successful: successful.length,
      failed: failed.length,
      statusCodes
    },
    workingEndpoints: {
      organization: orgEndpointsWorking,
      shipment: shipmentEndpointsWorking,
      user: userEndpointsWorking
    },
    allResults: results
  };
  
  fs.writeFileSync('api-discovery-results.json', JSON.stringify(detailedResults, null, 2));
  console.log('\n💾 Detailed results saved to api-discovery-results.json');
}

// Run the discovery
discoverEndpoints().catch(console.error);
