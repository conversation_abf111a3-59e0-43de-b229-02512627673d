# Fr8+ Organization API Flow Testing

This document provides instructions for testing the organization API flow in the Fr8+ application.

## API Endpoints

The application interacts with the following key endpoints:

```bash
# Fetch all organizations (no authentication needed)
GET http://localhost:4030/api/v1/organization/

# Get specific organization details (with org ID header)
GET http://localhost:4030/api/v1/organization/1
Header: X-Organization-ID: 1

# Most other endpoints require the organization context header
GET http://localhost:4030/api/v1/status/all
Header: X-Organization-ID: 1
```

## Testing Flow

### 1. Login
- Any email/password combination will work (uses mock authentication)
- After login, you will be redirected to the organization selector

### 2. Organization Selection
- The application will automatically fetch all organizations from the API
- Organizations are loaded from: `http://localhost:4030/api/v1/organization/`
- The first organization will be pre-selected in the dropdown
- Enter any PIN (validation is mocked in the current implementation)
- Click "Continue to Dashboard"

### 3. Dashboard Access
- All API requests will include the organization ID header: `X-Organization-ID`
- Operations that modify data would also include the PIN: `X-Organization-PIN`
- The organization name should appear in the header
- The user can switch organizations from the dropdown in the header

## Debugging

If you encounter issues:
- Check browser console for API request/response logs
- Verify API server is running at http://localhost:4030
- Check network tab for any 4xx/5xx responses
- Ensure headers are being properly included with each request

## Current Implementation

- Organization context is set globally via the OrganizationContext
- API client automatically adds organization headers to all requests
- Organization selection state is persisted in localStorage
- Organization ID and PIN are used to initialize the API client
