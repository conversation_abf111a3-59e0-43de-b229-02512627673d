import type { <PERSON><PERSON><PERSON> } from "next";
import { Manrope, Inter } from "next/font/google";
import "./globals.css";
import { OrganizationProvider } from "@/contexts/OrganizationContext";
import { AuthProvider } from "@/contexts/AuthContext";
import AnalyticsProvider from "@/components/analytics/analytics-provider";

const manrope = Manrope({
  subsets: ["latin"],
  variable: "--font-manrope",
  weight: ["300", "400", "500", "600", "700", "800"],
});

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  weight: ["300", "400", "500", "600", "700"],
});

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const isProduction = process.env.NODE_ENV === 'production';

  return (
    <html lang="en">
      <body className={`${manrope.variable} ${inter.variable} font-inter antialiased`}>
        {/* LinkedIn Analytics Tracking - Only in Production */}
        {isProduction && (
          <>
            <script 
              type="text/javascript" 
              dangerouslySetInnerHTML={{
                __html: `
                  _linkedin_partner_id = "8469689"; 
                  window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || []; 
                  window._linkedin_data_partner_ids.push(_linkedin_partner_id);
                `
              }}
            />
            <script 
              type="text/javascript" 
              dangerouslySetInnerHTML={{
                __html: `
                  (function(l) { 
                    if (!l){
                      window.lintrk = function(a,b){window.lintrk.q.push([a,b])}; 
                      window.lintrk.q=[]
                    } 
                    var s = document.getElementsByTagName("script")[0]; 
                    var b = document.createElement("script"); 
                    b.type = "text/javascript";
                    b.async = true; 
                    b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js"; 
                    s.parentNode.insertBefore(b, s);
                  })(window.lintrk);
                `
              }}
            />
            <noscript>
              <img 
                height="1" 
                width="1" 
                style={{display: 'none'}} 
                alt="" 
                src="https://px.ads.linkedin.com/collect/?pid=8469689&fmt=gif" 
              />
            </noscript>
          </>
        )}
        
        <AuthProvider>
          <OrganizationProvider>
            <AnalyticsProvider>
              {children}
            </AnalyticsProvider>
          </OrganizationProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

export const metadata: Metadata = {
  // Basic metadata
  title: "PACE - Freight Intelligence Copilot",
  description:
    "AI-powered freight brokerage assistant with voice control and proactive monitoring",

  // OpenGraph metadata - this determines how links appear when shared
  openGraph: {
    title: "PACE - Freight Intelligence Copilot",
    description: "Voice-controlled freight operations with AI-powered intelligence",
    url: "https://gopace.app",
    siteName: "PACE Freight Solutions",
    images: [
      {
        url: "https://gopace.app/opengraph-image.png", // Ensure this image exists in your public folder
        width: 1200,
        height: 630,
        alt: "PACE Freight Brokerage Copilot",
      },
    ],
    locale: "en_US",
    type: "website",
  },

  // Twitter-specific metadata
  twitter: {
    card: "summary_large_image",
    title: "PACE - Freight Intelligence Copilot",
    description: "Voice-controlled freight operations with AI-powered intelligence",
    images: ["https://gopace.app/opengraph-image.png"], // Ensure this image exists in your public folder
    creator: "@pace_freight", // Replace with your actual Twitter handle if different
  },

  // Icons/Favicon configuration
  icons: {
    icon: [
      { url: "/assets/favicon.ico" }, // Adjusted path
      { url: "/assets/icon1.png", type: "image/png" }, // Adjusted path, assuming icon1.png is intended
    ],
    apple: [
      { url: "/assets/apple-icon.png" }, // Adjusted path
    ],
  },
};
