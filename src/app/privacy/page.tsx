"use client";

import { trackPageView } from "@/lib/analytics";
import { useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { NavigationHeader } from "@/components/layout/navigation-header";
import Link from "next/link";

export default function PrivacyPage() {
  useEffect(() => {
    trackPageView("Privacy Policy");
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <NavigationHeader />
      
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-teal-600/5"></div>
      <div className="absolute top-0 left-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-purple-400/10 rounded-full blur-3xl"></div>
      
      <main className="flex-1 pt-24 sm:pt-32 pb-12 px-4 relative">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <Card className="border-none shadow-lg bg-white/70 backdrop-blur-sm">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-manrope font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                Privacy Policy
              </CardTitle>
              <p className="text-muted-foreground mt-2 font-inter">Last updated: June 4, 2025</p>
            </CardHeader>
            <CardContent className="space-y-6 text-muted-foreground font-inter">
              <section className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground font-manrope">Information We Collect</h2>
                <p>
                  We collect information you provide directly to us, including when you create an account, use our services, 
                  or communicate with us. This may include:
                </p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Name and contact information</li>
                  <li>Organization details and credentials</li>
                  <li>Shipping and tracking information</li>
                  <li>Usage data and analytics</li>
                </ul>
              </section>

              <section className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground font-manrope">How We Use Your Information</h2>
                <p>We use the information we collect to:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Provide and improve our services</li>
                  <li>Process and track shipments</li>
                  <li>Communicate with you about our services</li>
                  <li>Analyze usage patterns and optimize performance</li>
                  <li>Protect against fraud and unauthorized access</li>
                </ul>
              </section>

              <section className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground font-manrope">Data Protection</h2>
                <p>
                  We implement appropriate technical and organizational measures to protect your personal information against 
                  unauthorized or unlawful processing, accidental loss, destruction, or damage.
                </p>
              </section>

              <section className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground font-manrope">Your Rights</h2>
                <p>You have the right to:</p>
                <ul className="list-disc pl-6 space-y-2">
                  <li>Access your personal information</li>
                  <li>Correct inaccurate information</li>
                  <li>Request deletion of your information</li>
                  <li>Object to processing of your information</li>
                  <li>Request transfer of your information</li>
                </ul>
              </section>

              <section className="space-y-4">
                <h2 className="text-xl font-semibold text-foreground font-manrope">Contact Us</h2>
                <p>
                  If you have any questions about this Privacy Policy or our practices, please contact us at:
                </p>
                <div className="flex gap-4 pt-2">
                  <Button asChild className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 font-inter">
                    <Link href="/contact">Contact Us</Link>
                  </Button>
                  <Button variant="outline" asChild className="font-inter">
                    <Link href="/support">Support</Link>
                  </Button>
                </div>
              </section>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
