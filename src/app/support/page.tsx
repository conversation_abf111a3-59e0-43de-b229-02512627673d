"use client";

import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { NavigationHeader } from "@/components/layout/navigation-header";
import Link from "next/link";
import { trackFeatureUsage } from "@/lib/analytics";

export default function SupportPage() {
  useEffect(() => {
    trackFeatureUsage("support_page_viewed");
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <NavigationHeader />
      
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-teal-600/5"></div>
      <div className="absolute top-0 left-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-purple-400/10 rounded-full blur-3xl"></div>
      
      <main className="flex-1 pt-24 sm:pt-32 pb-12 px-4 relative">
        <Card className="max-w-4xl mx-auto border-none shadow-lg bg-white/70 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-3xl font-manrope font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
              Support Center
            </CardTitle>
            <p className="text-muted-foreground mt-2 font-inter">
              Find answers to common questions or contact our support team for assistance.
            </p>
          </CardHeader>
          <CardContent className="space-y-8 font-inter">
            <section>
              <h2 className="text-2xl font-semibold mb-4 font-manrope">Frequently Asked Questions</h2>
              <Accordion type="single" collapsible className="w-full">
                <AccordionItem value="shipment-tracking">
                  <AccordionTrigger>How do I track my shipment?</AccordionTrigger>
                  <AccordionContent>
                    You can track your shipment by entering the tracking number in the search bar on the dashboard. 
                    The system will show you real-time updates on your shipment&apos;s location and status.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="account-setup">
                  <AccordionTrigger>How do I set up my account?</AccordionTrigger>
                  <AccordionContent>
                    To set up your account, click on the &quot;Sign Up&quot; button and follow the registration process. 
                    You&apos;ll need to provide basic information about your organization and verify your email address.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="billing">
                  <AccordionTrigger>How does billing work?</AccordionTrigger>
                  <AccordionContent>
                    Our billing is based on your subscription plan and usage. You can view your current plan 
                    and billing details in the account settings. We offer monthly and annual billing options.
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem value="integration">
                  <AccordionTrigger>Can I integrate PACE with my existing systems?</AccordionTrigger>
                  <AccordionContent>
                    Yes, PACE offers API integration capabilities. Detailed documentation is coming soon - we are currently working on it. Contact our technical support team for assistance with integration.
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </section>

            <section className="space-y-4">
              <h2 className="text-2xl font-semibold font-manrope">Need More Help?</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-6 bg-white/50 backdrop-blur-sm border border-blue-100">
                  <h3 className="text-xl font-semibold mb-2 font-manrope">Contact Support</h3>
                  <p className="text-muted-foreground mb-4">
                    Our support team is available 24/7 to help you with any questions.
                  </p>
                  <Link href="/contact">
                    <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 font-inter">
                      Contact Us
                    </Button>
                  </Link>
                </Card>

                <Card className="p-6 bg-white/50 backdrop-blur-sm border border-teal-100">
                  <h3 className="text-xl font-semibold mb-2 font-manrope">Documentation</h3>
                  <p className="text-muted-foreground mb-4">
                    Browse our detailed documentation for guides and tutorials. Coming soon - we are working on it!
                  </p>
                  <Button variant="outline" className="font-inter" disabled>
                    Coming Soon
                  </Button>
                </Card>
              </div>
            </section>

            <section className="border-t pt-6">
              <h2 className="text-2xl font-semibold mb-4 font-manrope">Support Hours</h2>
              <p className="text-muted-foreground">
                Our support team is available Monday through Friday, 9:00 AM - 6:00 PM EST.
                For urgent issues outside these hours, please use our emergency support line.
              </p>
            </section>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
