"use client";
import { useEffect } from "react"
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Package, Shield, FileText, Zap, CheckCircle, Star, Clock, MessageSquare, TrendingDown, DollarSign, AlertTriangle, Building } from "lucide-react";
import { useAuth } from "@/contexts/AuthContext";
import { NavigationHeader } from "@/components/layout/navigation-header";
import dynamic from "next/dynamic";
import { WaitlistWidget } from "@/components/shared/waitlist-widget";

// Dynamically import the WorldMap component to avoid SSR issues
const WorldMap = dynamic(() => import("@/components/layout/world-map"), {
  ssr: false,
  loading: () => <div className="h-full w-full bg-black" />
});

// Simple wrapper component for the WorldMap
const WorldMapWrapper = () => <WorldMap className="h-full" />;

export default function LandingPage() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();
  
  useEffect(() => {
    // Track page view with LogRocket
    import('@/lib/analytics').then(({ trackPageView }) => {
      trackPageView('Landing Page');
    });
  }, []);

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <NavigationHeader />
      
      {/* Hero section */}
      <main className="flex-1">
        <section className="relative py-20 sm:py-32 md:py-40 overflow-hidden min-h-screen flex items-center">
          {/* Background Elements */}
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-teal-600/5"></div>
          <div className="absolute top-0 left-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 right-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-purple-400/10 rounded-full blur-3xl"></div>
          
          <div className="container mx-auto px-4 sm:px-6 text-center space-y-6 sm:space-y-8 relative">
            <div className="space-y-4 sm:space-y-6 max-w-4xl mx-auto">
              {/* AI Badge */}
              <div className="inline-flex items-center gap-2 px-3 sm:px-4 py-1.5 sm:py-2 bg-blue-50 border border-blue-200 rounded-full">
                <Zap className="h-3 w-3 sm:h-4 sm:w-4 text-blue-600" />
                <span className="text-xs sm:text-sm font-inter font-medium text-blue-700">Powered by AI Technology</span>
              </div>
              
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-7xl font-manrope font-black text-gray-900 leading-tight tracking-tight">
                AI Copilot for
                <br />
                <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent font-black">
                  Freight Brokerage
                </span>
              </h1>
              
              <p className="text-base sm:text-lg md:text-xl lg:text-2xl font-inter text-gray-600 leading-relaxed max-w-3xl mx-auto px-4 sm:px-0">
                Revolutionize your logistics operations with intelligent automation, 
                real-time insights, and seamless workflow management.
              </p>
              
              <div className="flex justify-center pt-6 sm:pt-8">
                <Button 
                  size="lg" 
                  className="px-6 sm:px-12 py-3 sm:py-4 text-base sm:text-lg font-inter font-semibold bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300 rounded-lg active:scale-95 touch-manipulation min-h-[48px]"
                  onClick={() => {
                    if (isAuthenticated) {
                      router.push("/dashboard");
                    } else {
                      router.push("/login");
                    }
                  }}
                  role="button"
                  aria-label="Get started with PACE"
                >
                  Get Started <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </div>

              {/* Dashboard Preview */}
              <div className="pt-12 sm:pt-16 md:pt-20">
                <div className="text-center mb-8">
                  <p className="text-sm sm:text-base font-inter text-gray-600 mb-4">
                    See what you&apos;ll get with PACE
                  </p>
                </div>
                
                <div className="relative max-w-3xl lg:max-w-6xl xl:max-w-7xl mx-auto px-4 sm:px-6">
                  {/* Browser mockup frame */}
                  <div className="bg-white rounded-t-xl shadow-2xl border border-gray-200">
                    <div className="flex items-center gap-2 px-4 py-3 border-b border-gray-200">
                      <div className="flex gap-2">
                        <div className="w-3 h-3 rounded-full bg-red-400"></div>
                        <div className="w-3 h-3 rounded-full bg-yellow-400"></div>
                        <div className="w-3 h-3 rounded-full bg-green-400"></div>
                      </div>
                      <div className="flex-1 text-center">
                        <div className="bg-gray-100 rounded-md px-3 py-1 text-xs text-gray-600 inline-block">
                          app.pace.ai/dashboard
                        </div>
                      </div>
                    </div>
                    
                    {/* Dashboard screenshot */}
                    <div className="relative overflow-hidden rounded-b-xl">
                      <Image
                        src="/dashboard.png"
                        alt="PACE Dashboard - Real-time freight management overview"
                        width={1400}
                        height={900}
                        className="w-full h-auto object-cover object-top"
                        priority
                      />
                      
                      {/* Gradient overlay for better integration */}
                      <div className="absolute inset-0 bg-gradient-to-t from-white/10 via-transparent to-transparent pointer-events-none"></div>
                    </div>
                  </div>
                  
                  {/* Floating elements for visual appeal */}
                  <div className="absolute -top-4 -left-4 w-8 h-8 bg-blue-500 rounded-full blur-sm opacity-60 animate-pulse"></div>
                  <div className="absolute -top-2 -right-6 w-6 h-6 bg-purple-500 rounded-full blur-sm opacity-40 animate-pulse" style={{ animationDelay: '1s' }}></div>
                  <div className="absolute -bottom-4 left-8 w-10 h-10 bg-teal-500 rounded-full blur-sm opacity-50 animate-pulse" style={{ animationDelay: '2s' }}></div>
                </div>
              </div>
              
              {/* Trust indicators */}
              <div className="flex flex-wrap justify-center items-center gap-4 sm:gap-8 pt-8 sm:pt-12 opacity-70">
                <div className="flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-600" />
                  <span className="text-xs sm:text-sm font-inter text-gray-600">SOC 2 Compliant</span>
                </div>
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                  <span className="text-xs sm:text-sm font-inter text-gray-600">Enterprise Security</span>
                </div>
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500" />
                  <span className="text-xs sm:text-sm font-inter text-gray-600">99.9% Uptime</span>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Features section */}
        <section id="features" className="min-h-screen bg-gray-50">
          <div className="min-h-screen flex flex-col justify-center py-12 sm:py-20">
            <div className="container mx-auto px-4 sm:px-6">
              <div className="text-center mb-12 sm:mb-20 space-y-4 sm:space-y-6">
                <div className="inline-flex items-center gap-2 px-4 sm:px-6 py-2 sm:py-3 bg-blue-50 border border-blue-200 rounded-full">
                  <Zap className="h-4 w-4 sm:h-5 sm:w-5 text-blue-600" />
                  <span className="text-sm sm:text-base font-inter font-medium text-blue-700">Key Industry Problems We Solve</span>
                </div>
                <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-manrope font-semibold text-gray-900 leading-tight px-4 sm:px-0">
                  Transform Your Freight Operations 
                  <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    From Reactive to Proactive
                  </span>
                </h2>
                <p className="text-base sm:text-lg md:text-xl lg:text-2xl font-inter text-gray-600 max-w-3xl mx-auto leading-relaxed px-4 sm:px-0">
                  Solve critical freight challenges with AI-powered intelligence and voice-controlled operations
                </p>
              </div>
              
              <div className="space-y-20 sm:space-y-40">
                {/* Shipment Visibility & Tracking Problem/Solution - Bento Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
                  {/* Feature Title - Full Width */}
                  <div className="lg:col-span-3 mb-2 sm:mb-4">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl font-manrope font-semibold mb-2">
                      <span className="bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                        Shipment Visibility & Tracking
                      </span>
                    </h2>
                  </div>

                  {/* Large Hero Image - 2/3 width */}
                  <div className="lg:col-span-2 rounded-xl overflow-hidden h-[300px] sm:h-[400px] lg:h-[500px] relative shadow-lg border border-blue-100">
                    <Image
                      src="/assets/tracking.jpg"
                      alt="Real-time Tracking Intelligence"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tr from-blue-600/30 to-teal-500/20"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-6 bg-gradient-to-t from-black/80 to-transparent">
                      <h3 className="text-lg sm:text-xl lg:text-2xl font-manrope font-semibold text-white">See Every Shipment, Every Step</h3>
                    </div>
                  </div>

                  {/* Problem Card - 1/3 width */}
                  <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-4 sm:p-6 rounded-xl shadow-md border border-slate-200 flex flex-col justify-between h-[300px] sm:h-[400px] lg:h-[500px]">
                    <div>
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-slate-200/70 rounded-full mb-4">
                        <Package className="h-5 w-5 text-slate-700" />
                        <span className="text-sm font-inter font-medium text-slate-800">Industry Challenge</span>
                      </div>
                      <h3 className="text-2xl font-manrope font-semibold text-slate-900 mb-4">Visibility & Tracking Chaos</h3>
                      <div className="space-y-3 mt-6">
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">Multiple tracking systems causing blind spots</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">47% of shipments experience status gaps</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">          Critical updates missed until it&apos;s too late</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Solution Card - Full Width */}
                  <div className="lg:col-span-3 bg-gradient-to-br from-blue-50 to-teal-50 p-8 rounded-xl shadow-md border border-blue-100">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-blue-100/70 rounded-full mb-4">
                      <CheckCircle className="h-5 w-5 text-teal-600" />
                      <span className="text-sm font-inter font-medium text-blue-700">PACE Solution</span>
                    </div>
                    <h3 className="text-3xl md:text-4xl font-manrope font-semibold mb-4 bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
                      Unified Real-Time Tracking Intelligence
                    </h3>
                    <p className="text-xl font-inter text-gray-700 leading-relaxed mb-8 max-w-4xl">
                      Get 100% visibility across all carriers with AI-enhanced unified tracking. Voice commands like &quot;Show me delayed shipments in Chicago&quot; instantly reveal issues with proactive resolution options, reducing tracking-related calls by 78% and ensuring zero blind spots.
                    </p>
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">Multi-carrier tracking</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">Predictive alerts</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">Voice-activated insights</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-blue-100/70 rounded-full">
                        <Star className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-inter font-medium text-blue-700">78% fewer tracking calls</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Communication & Coordination Problem/Solution - Bento Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Feature Title - Full Width */}
                  <div className="lg:col-span-3 mb-4">
                    <h2 className="text-3xl md:text-4xl font-manrope font-semibold mb-2">
                      <span className="bg-gradient-to-r from-teal-500 to-green-500 bg-clip-text text-transparent">
                        Communication & Coordination
                      </span>
                    </h2>
                  </div>

                  {/* Problem Card - 1/3 width */}
                  <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-6 rounded-xl shadow-md border border-slate-200 flex flex-col justify-between">
                    <div>
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-slate-200/70 rounded-full mb-4">
                        <MessageSquare className="h-5 w-5 text-slate-700" />
                        <span className="text-sm font-inter font-medium text-slate-800">Industry Challenge</span>
                      </div>
                      <h3 className="text-2xl font-manrope font-semibold text-slate-900 mb-4">Communication Bottlenecks</h3>
                      <div className="space-y-3 mt-6">
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">3.5 hours daily wasted on status calls</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">62% of updates never reach all stakeholders</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">Critical delays from disconnected communication</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Large Hero Image - 2/3 width */}
                  <div className="lg:col-span-2 rounded-xl overflow-hidden h-[400px] relative shadow-lg border border-green-100">
                    <Image
                      src="/assets/office.jpg"
                      alt="Voice Communication Hub"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tr from-teal-500/30 to-green-500/20"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
                      <h3 className="text-2xl font-manrope font-semibold text-white">Seamless Communication Across Teams</h3>
                    </div>
                  </div>

                  {/* Solution Card - Full Width */}
                  <div className="lg:col-span-3 bg-gradient-to-br from-teal-50 to-green-50 p-8 rounded-xl shadow-md border border-teal-100">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-green-100/70 rounded-full mb-4">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <span className="text-sm font-inter font-medium text-green-700">PACE Solution</span>
                    </div>
                    <h3 className="text-3xl md:text-4xl font-manrope font-semibold mb-4 bg-gradient-to-r from-teal-500 to-green-500 bg-clip-text text-transparent">
                      Voice-Powered Communication Hub
                    </h3>
                    <p className="text-xl font-inter text-gray-700 leading-relaxed mb-8 max-w-4xl">
                      Eliminate endless phone calls with voice commands. Say &ldquo;Update all stakeholders on TRACK8472&rdquo; and PACE automatically notifies shippers, carriers, and receivers with tailored information through their preferred channels, saving 27 hours weekly and ensuring 100% information delivery.
                    </p>
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">Automated notifications</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-green-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-inter font-medium text-green-700">Voice commands</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-blue-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-inter font-medium text-blue-700">73% fewer status calls</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <Star className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">100% stakeholder coverage</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Proactive Problem Management - Bento Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Feature Title - Full Width */}
                  <div className="lg:col-span-3 mb-4">
                    <h2 className="text-3xl md:text-4xl font-manrope font-semibold mb-2">
                      <span className="bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
                        Proactive Problem Management
                      </span>
                    </h2>
                  </div>

                  {/* Large Hero Image - 2/3 width */}
                  <div className="lg:col-span-2 rounded-xl overflow-hidden h-[500px] relative shadow-lg border border-purple-100 order-1 lg:order-1">
                    <Image
                      src="/assets/trains.jpg"
                      alt="Proactive Problem Management"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/30 to-purple-500/20"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
                      <h3 className="text-2xl font-manrope font-semibold text-white">Anticipate & Resolve Before Impact</h3>
                    </div>
                  </div>

                  {/* Problem Card - 1/3 width */}
                  <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-6 rounded-xl shadow-md border border-slate-200 flex flex-col justify-between h-[500px] order-2 lg:order-2">
                    <div>
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-slate-200/70 rounded-full mb-4">
                        <AlertTriangle className="h-5 w-5 text-slate-700" />
                        <span className="text-sm font-inter font-medium text-slate-800">Industry Challenge</span>
                      </div>
                      <h3 className="text-2xl font-manrope font-semibold text-slate-900 mb-4">Problem Management Failures</h3>
                      <div className="space-y-3 mt-6">
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">84% of issues discovered after impact</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">$1,250 avg. cost per disruption</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">3.7 days average recovery time</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Solution Card - Full Width */}
                  <div className="lg:col-span-3 bg-gradient-to-br from-blue-50 to-purple-50 p-8 rounded-xl shadow-md border border-blue-100 order-3">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-purple-100/70 rounded-full mb-4">
                      <CheckCircle className="h-5 w-5 text-purple-600" />
                      <span className="text-sm font-inter font-medium text-purple-700">PACE Solution</span>
                    </div>
                    <h3 className="text-3xl md:text-4xl font-manrope font-semibold mb-4 bg-gradient-to-r from-blue-500 to-purple-500 bg-clip-text text-transparent">
                      AI-Powered Proactive Monitoring
                    </h3>
                    <p className="text-xl font-inter text-gray-700 leading-relaxed mb-8 max-w-4xl">
                      Detect and solve problems before they happen. PACE continuously monitors 27 risk factors 24/7 and suggests cost-optimized solutions like &ldquo;Expedite customs for $250 to save 2 days&rdquo; with one-click implementation, reducing disruption costs by 86% and cutting recovery time to under 4 hours.
                    </p>
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2 px-4 py-2 bg-blue-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-inter font-medium text-blue-700">Predictive intelligence</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-purple-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-inter font-medium text-purple-700">Solution recommendations</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">92% faster resolution</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-purple-100/70 rounded-full">
                        <Star className="h-4 w-4 text-purple-600" />
                        <span className="text-sm font-inter font-medium text-purple-700">86% lower disruption costs</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Documentation & Compliance Problem/Solution - Bento Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Feature Title - Full Width */}
                  <div className="lg:col-span-3 mb-4">
                    <h2 className="text-3xl md:text-4xl font-manrope font-semibold mb-2">
                      <span className="bg-gradient-to-r from-teal-500 to-blue-500 bg-clip-text text-transparent">
                        Documentation & Compliance
                      </span>
                    </h2>
                  </div>

                  {/* Problem Card - 1/3 width */}
                  <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-6 rounded-xl shadow-md border border-slate-200 flex flex-col justify-between">
                    <div>
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-slate-200/70 rounded-full mb-4">
                        <FileText className="h-5 w-5 text-slate-700" />
                        <span className="text-sm font-inter font-medium text-slate-800">Industry Challenge</span>
                      </div>
                      <h3 className="text-2xl font-manrope font-semibold text-slate-900 mb-4">Documentation Burden</h3>
                      <div className="space-y-3 mt-6">
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">2.5 hours per shipment on documentation</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">22% error rate in manual BOLs</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">$3,700 avg. compliance penalty</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Large Hero Image - 2/3 width */}
                  <div className="lg:col-span-2 rounded-xl overflow-hidden h-[400px] relative shadow-lg border border-teal-100">
                    <Image
                      src="/assets/trucks.jpg"
                      alt="Document Automation"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tr from-teal-500/30 to-blue-500/20"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
                      <h3 className="text-2xl font-manrope font-semibold text-white">Flawless Documentation, Every Time</h3>
                    </div>
                  </div>

                  {/* Solution Card - Full Width */}
                  <div className="lg:col-span-3 bg-gradient-to-br from-teal-50 to-blue-50 p-8 rounded-xl shadow-md border border-teal-100">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-teal-100/70 rounded-full mb-4">
                      <CheckCircle className="h-5 w-5 text-teal-600" />
                      <span className="text-sm font-inter font-medium text-teal-700">PACE Solution</span>
                    </div>
                    <h3 className="text-3xl md:text-4xl font-manrope font-semibold mb-4 bg-gradient-to-r from-teal-500 to-blue-500 bg-clip-text text-transparent">
                      Voice-Triggered Document Automation
                    </h3>
                    <p className="text-xl font-inter text-gray-700 leading-relaxed mb-8 max-w-4xl">
                      Generate perfect BOLs, invoices, and customs documentation with voice commands. Say &ldquo;Create BOL for shipment ABC123&rdquo; and get error-free documents instantly with AI validation across 17 compliance frameworks. Documentation time reduced by 94% with zero compliance violations.
                    </p>
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">Voice generation</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-blue-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-inter font-medium text-blue-700">AI validation</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-teal-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-teal-600" />
                        <span className="text-sm font-inter font-medium text-teal-700">Zero compliance errors</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-blue-100/70 rounded-full">
                        <Star className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-inter font-medium text-blue-700">94% time reduction</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {/* Efficiency & Optimization Challenges - Bento Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Feature Title - Full Width */}
                  <div className="lg:col-span-3 mb-4">
                    <h2 className="text-3xl md:text-4xl font-manrope font-semibold mb-2">
                      <span className="bg-gradient-to-r from-blue-500 to-green-500 bg-clip-text text-transparent">
                        Efficiency & Optimization
                      </span>
                    </h2>
                  </div>

                  {/* Large Hero Image - 2/3 width */}
                  <div className="lg:col-span-2 rounded-xl overflow-hidden h-[500px] relative shadow-lg border border-blue-100 order-1">
                    <Image
                      src="/assets/tracking.jpg"
                      alt="Optimization Engine"
                      fill
                      className="object-cover"
                    />
                    <div className="absolute inset-0 bg-gradient-to-tr from-blue-500/30 to-green-500/20"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-6 bg-gradient-to-t from-black/80 to-transparent">
                      <h3 className="text-2xl font-manrope font-semibold text-white">Maximize Resources & Minimize Waste</h3>
                    </div>
                  </div>

                  {/* Problem Card - 1/3 width */}
                  <div className="bg-gradient-to-br from-slate-50 to-slate-100 p-6 rounded-xl shadow-md border border-slate-200 flex flex-col justify-between h-[500px] order-2">
                    <div>
                      <div className="inline-flex items-center gap-2 px-4 py-2 bg-slate-200/70 rounded-full mb-4">
                        <TrendingDown className="h-5 w-5 text-slate-700" />
                        <span className="text-sm font-inter font-medium text-slate-800">Industry Challenge</span>
                      </div>
                      <h3 className="text-2xl font-manrope font-semibold text-slate-900 mb-4">Optimization Challenges</h3>
                      <div className="space-y-3 mt-6">
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">31% of freight capacity underutilized</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">$0.43 per mile wasted on inefficient routing</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <div className="bg-red-100 p-1 rounded-md mt-1">
                            <AlertTriangle className="h-4 w-4 text-red-600" />
                          </div>
                          <p className="text-slate-700">18% higher costs due to manual planning</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Solution Card - Full Width */}
                  <div className="lg:col-span-3 bg-gradient-to-br from-blue-50 to-green-50 p-8 rounded-xl shadow-md border border-blue-100 order-3">
                    <div className="inline-flex items-center gap-3 px-4 py-2 bg-blue-100/70 rounded-full mb-4">
                      <CheckCircle className="h-5 w-5 text-blue-600" />
                      <span className="text-sm font-inter font-medium text-blue-700">PACE Solution</span>
                    </div>
                    <h3 className="text-3xl md:text-4xl font-manrope font-semibold mb-4 bg-gradient-to-r from-blue-500 to-green-500 bg-clip-text text-transparent">
                      AI-Powered Optimization Engine
                    </h3>
                    <p className="text-xl font-inter text-gray-700 leading-relaxed mb-8 max-w-4xl">
                      Maximize operational efficiency with intelligent resource allocation. Voice-activate load consolidation, optimal carrier selection, and route optimization to reduce empty miles by 27% and cut transportation costs by 23% with continuous machine learning.
                    </p>
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center gap-2 px-4 py-2 bg-blue-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-inter font-medium text-blue-700">Smart load consolidation</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-green-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-inter font-medium text-green-700">Dynamic route planning</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-blue-100/70 rounded-full">
                        <CheckCircle className="h-4 w-4 text-blue-600" />
                        <span className="text-sm font-inter font-medium text-blue-700">AI-powered resource matching</span>
                      </div>
                      <div className="flex items-center gap-2 px-4 py-2 bg-green-100/70 rounded-full">
                        <Star className="h-4 w-4 text-green-600" />
                        <span className="text-sm font-inter font-medium text-green-700">23% avg. cost reduction</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Early Access Section */}
              <section className="w-full max-w-6xl mx-auto px-4 py-16 text-center">
                <h2 className="text-3xl font-bold mb-4">Get Early Access</h2>
                <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
                  Join our waitlist to be among the first to experience PACE - the next generation of freight intelligence.
                  Get exclusive early access and special launch benefits.
                </p>
                <div className="max-w-md mx-auto bg-white/50 backdrop-blur-sm p-6 rounded-xl border border-gray-100 shadow-xl">
                  <WaitlistWidget />
                </div>
              </section>

              {/* Bottom Stats */}
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-5 gap-4 sm:gap-6 mt-16 sm:mt-32 max-w-5xl mx-auto">
                <div className="text-center p-4 sm:p-6 bg-white rounded-lg shadow-lg border border-blue-100">
                  <div className="w-10 h-10 sm:w-14 sm:h-14 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                    <Clock className="h-5 w-5 sm:h-7 sm:w-7 text-blue-600" />
                  </div>
                  <h4 className="text-sm sm:text-lg font-manrope font-semibold text-gray-900 mb-2">Time Saved</h4>
                  <p className="font-inter text-blue-600 font-bold text-lg sm:text-xl mb-1">78%</p>
                  <p className="font-inter text-gray-600 text-xs sm:text-sm">Reduction in tracking time</p>
                </div>

                <div className="text-center p-4 sm:p-6 bg-white rounded-lg shadow-lg border border-green-100">
                  <div className="w-10 h-10 sm:w-14 sm:h-14 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                    <MessageSquare className="h-5 w-5 sm:h-7 sm:w-7 text-green-600" />
                  </div>
                  <h4 className="text-sm sm:text-lg font-manrope font-semibold text-gray-900 mb-2">Communication</h4>
                  <p className="font-inter text-green-600 font-bold text-lg sm:text-xl mb-1">100%</p>
                  <p className="font-inter text-gray-600 text-xs sm:text-sm">Stakeholder coverage</p>
                </div>

                <div className="text-center p-4 sm:p-6 bg-white rounded-lg shadow-lg border border-purple-100">
                  <div className="w-10 h-10 sm:w-14 sm:h-14 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                    <AlertTriangle className="h-5 w-5 sm:h-7 sm:w-7 text-purple-600" />
                  </div>
                  <h4 className="text-sm sm:text-lg font-manrope font-semibold text-gray-900 mb-2">Issue Prevention</h4>
                  <p className="font-inter text-purple-600 font-bold text-lg sm:text-xl mb-1">92%</p>
                  <p className="font-inter text-gray-600 text-xs sm:text-sm">Faster resolution time</p>
                </div>
                
                <div className="text-center p-4 sm:p-6 bg-white rounded-lg shadow-lg border border-teal-100">
                  <div className="w-10 h-10 sm:w-14 sm:h-14 bg-teal-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                    <FileText className="h-5 w-5 sm:h-7 sm:w-7 text-teal-600" />
                  </div>
                  <h4 className="text-sm sm:text-lg font-manrope font-semibold text-gray-900 mb-2">Documentation</h4>
                  <p className="font-inter text-teal-600 font-bold text-lg sm:text-xl mb-1">94%</p>
                  <p className="font-inter text-gray-600 text-xs sm:text-sm">Time reduction</p>
                </div>

                <div className="text-center p-4 sm:p-6 bg-white rounded-lg shadow-lg border border-amber-100 col-span-2 sm:col-span-1">
                  <div className="w-10 h-10 sm:w-14 sm:h-14 bg-amber-100 rounded-lg flex items-center justify-center mx-auto mb-3 sm:mb-4">
                    <DollarSign className="h-5 w-5 sm:h-7 sm:w-7 text-amber-600" />
                  </div>
                  <h4 className="text-sm sm:text-lg font-manrope font-semibold text-gray-900 mb-2">Cost Savings</h4>
                  <p className="font-inter text-amber-600 font-bold text-lg sm:text-xl mb-1">23%</p>
                  <p className="font-inter text-gray-600 text-xs sm:text-sm">Transportation cost reduction</p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* About section */}
        <section id="about" className="py-24 bg-black relative overflow-hidden">
          {/* Gradient overlays for enhanced effect */}
          <div className="absolute top-0 left-0 right-0 h-40 bg-gradient-to-b from-black via-black/80 to-transparent z-10"></div>
          <div className="absolute bottom-0 left-0 right-0 h-40 bg-gradient-to-t from-black via-black/80 to-transparent z-10"></div>
          <div className="absolute left-0 top-0 bottom-0 w-40 bg-gradient-to-r from-black via-black/80 to-transparent z-10"></div>
          <div className="absolute right-0 top-0 bottom-0 w-40 bg-gradient-to-l from-black via-black/80 to-transparent z-10"></div>
          
          {/* World Map Background */}
          <div className="absolute inset-0 opacity-40">
            {/* WorldMap is imported dynamically to work with browser-only features */}
            {typeof window !== 'undefined' && (
              <WorldMapWrapper />
            )}
          </div>
          
          {/* Connection lines effect overlay */}
          <div className="absolute inset-0 opacity-20 overflow-hidden">
            <div className="absolute h-px w-full top-1/3 left-0 bg-gradient-to-r from-transparent via-blue-400 to-transparent"></div>
            <div className="absolute h-px w-full top-2/3 left-0 bg-gradient-to-r from-transparent via-teal-400 to-transparent"></div>
            <div className="absolute h-full w-px left-1/3 top-0 bg-gradient-to-b from-transparent via-blue-400 to-transparent"></div>
            <div className="absolute h-full w-px left-2/3 top-0 bg-gradient-to-b from-transparent via-teal-400 to-transparent"></div>
          </div>
          
          {/* Content overlay */}
          <div className="container mx-auto px-6 relative z-20">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mb-16 space-y-4">
                <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-900/80 to-teal-900/80 border border-blue-700/30 backdrop-blur-sm rounded-full">
                  <Building className="h-4 w-4 text-blue-400" />
                  <span className="text-sm font-inter font-medium text-blue-300">About PACE</span>
                </div>
                <h2 className="text-4xl md:text-5xl font-manrope font-semibold text-white">
                  Transforming Freight 
                  <span className="block bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">
                    with Intelligence
                  </span>
                </h2>
              </div>
              
              <div className="grid md:grid-cols-2 gap-12 items-center">
                <div className="space-y-6">
                  <p className="text-lg font-inter text-blue-100 leading-relaxed">
                    PACE is a cutting-edge logistics and freight intelligence platform designed to streamline operations
                    for shipping companies, freight forwarders, and logistics service providers.
                  </p>
                  <p className="text-lg font-inter text-blue-100 leading-relaxed">
                    Our platform provides real-time tracking, advanced reporting, and organization management
                    tools that help businesses optimize their supply chain operations and provide better service
                    to their customers.
                  </p>
                  <p className="text-lg font-inter text-blue-100 leading-relaxed">
                    With multi-tenant architecture, JWT authentication, and AI-powered insights, PACE is the ideal solution for
                    companies of all sizes who need robust logistics management capabilities.
                  </p>
                  
                  <div className="grid grid-cols-2 gap-6 pt-6">
                    <div className="text-center p-4 bg-blue-900/40 backdrop-blur-sm border border-blue-700/30 rounded-xl">
                      <div className="text-3xl font-manrope font-semibold text-blue-300">80%</div>
                      <div className="text-sm font-inter text-blue-200">Fewer PIN Prompts</div>
                    </div>
                    <div className="text-center p-4 bg-teal-900/40 backdrop-blur-sm border border-teal-700/30 rounded-xl">
                      <div className="text-3xl font-manrope font-semibold text-teal-300">500+</div>
                      <div className="text-sm font-inter text-teal-200">Automated Tasks</div>
                    </div>
                  </div>
                </div>
                
                <div className="relative">
                  <div className="backdrop-blur-md bg-gradient-to-br from-blue-900/40 to-teal-900/40 border border-blue-700/30 p-6 rounded-xl">
                    <h3 className="text-2xl font-manrope font-semibold mb-4 bg-gradient-to-r from-blue-400 to-teal-400 bg-clip-text text-transparent">
                      Global Coverage
                    </h3>
                    <div className="space-y-6">
                      <p className="text-blue-100">
                        Our network spans across 6 continents with active freight management in over 120 countries.
                      </p>
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <h4 className="text-teal-300 font-semibold mb-2">North America</h4>
                          <div className="space-y-1 text-blue-200">
                            <p>United States</p>
                            <p>Canada</p>
                            <p>Mexico</p>
                          </div>
                        </div>
                        <div>
                          <h4 className="text-blue-300 font-semibold mb-2">Europe</h4>
                          <div className="space-y-1 text-blue-200">
                            <p>United Kingdom</p>
                            <p>Germany</p>
                            <p>Netherlands</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between mt-4">
                        <div>
                          <div className="text-3xl font-semibold text-blue-300">27M+</div>
                          <div className="text-sm text-blue-200">Shipments Tracked</div>
                        </div>
                        <div>
                          <div className="text-3xl font-semibold text-teal-300">140+</div>
                          <div className="text-sm text-teal-200">Countries Served</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-gradient-to-r from-blue-600 to-teal-600 rounded-lg blur-3xl opacity-30"></div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-5 gap-8">
            <div className="md:col-span-2">
              <div className="flex items-center gap-3 mb-4">
                <Image
                  src="/assets/logo.png"
                  alt="PACE Logo"
                  width={32}
                  height={32}
                  className="h-8 w-8 rounded-sm"
                />
                <div>
                  <h1 className="text-2xl font-manrope font-bold">PACE</h1>
                  <p className="text-xs text-gray-400 font-inter">AI Copilot for Freight Brokerage</p>
                </div>
              </div>
              <p className="text-gray-400 font-inter leading-relaxed max-w-md">
                Revolutionizing logistics operations with intelligent automation, 
                real-time insights, and seamless workflow management.
              </p>
            </div>
            
            <div>
              <h3 className="font-manrope font-semibold mb-4">Company</h3>
              <ul className="space-y-2 font-inter text-sm text-gray-400">
                <li><Link href="/privacy" className="hover:text-white transition-colors cursor-pointer">Privacy</Link></li>
                <li><Link href="/terms" className="hover:text-white transition-colors cursor-pointer">Terms</Link></li>
                <li><Link href="/support" className="hover:text-white transition-colors cursor-pointer">Support</Link></li>
                <li><Link href="/contact" className="hover:text-white transition-colors cursor-pointer">Contact</Link></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-6 mb-4 md:mb-0">
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-green-400" />
                <span className="text-sm font-inter text-gray-400">SOC 2 Compliant</span>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-400" />
                <span className="text-sm font-inter text-gray-400">99.9% Uptime</span>
              </div>
            </div>              <div className="text-sm font-inter text-gray-400">
              © {new Date().getFullYear()} PACE Technologies. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

