"use client";

import { useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { NavigationHeader } from "@/components/layout/navigation-header";
import { trackFeatureUsage } from "@/lib/analytics";

export default function ContactPage() {
  useEffect(() => {
    trackFeatureUsage("contact_page_viewed");
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission logic here
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <NavigationHeader />
      
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-purple-600/5 to-teal-600/5"></div>
      <div className="absolute top-0 left-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-blue-400/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-48 h-48 sm:w-96 sm:h-96 bg-purple-400/10 rounded-full blur-3xl"></div>
      
      <main className="flex-1 pt-24 sm:pt-32 pb-12 px-4 relative">
        <Card className="max-w-4xl mx-auto border-none shadow-lg bg-white/70 backdrop-blur-sm">
          <CardHeader>
            <CardTitle className="text-3xl font-manrope font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
              Contact Us
            </CardTitle>
            <p className="text-muted-foreground mt-2 font-inter">
              Get in touch with our team. We&apos;re here to help!
            </p>
          </CardHeader>
          <CardContent className="space-y-8 font-inter">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <section>
              <h2 className="text-2xl font-semibold mb-4 font-manrope">Send us a message</h2>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input id="name" placeholder="Your name" required />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" placeholder="Your email" required />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject">Subject</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a subject" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="general">General Inquiry</SelectItem>
                      <SelectItem value="support">Technical Support</SelectItem>
                      <SelectItem value="billing">Billing Question</SelectItem>
                      <SelectItem value="feedback">Feedback</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    placeholder="How can we help you?"
                    className="min-h-[150px]"
                    required
                  />
                </div>

                <Button 
                  type="submit" 
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 font-inter"
                >
                  Send Message
                </Button>
              </form>
            </section>

            <section className="space-y-6">
              <div>
                <h2 className="text-2xl font-semibold mb-4 font-manrope">Other Ways to Reach Us</h2>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold">Email</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold">Office</h3>
                    <p className="text-muted-foreground">
                      Delta Corner Annex, Ring Rd Westlands Ln <br />
                      4th Floor <br />
                      Nairobi, Kenya 
                    </p>
                  </div>
                </div>
              </div>

              <div>
                <h2 className="text-2xl font-semibold mb-4 font-manrope">Business Hours</h2>
                <div className="space-y-2">
                  <p className="text-muted-foreground">Monday - Friday: 9:00 AM - 6:00 PM EST</p>
                  <p className="text-muted-foreground">Saturday - Sunday: Closed</p>
                  <p className="text-sm mt-4">
                    For urgent support outside business hours, please call our 24/7 emergency line.
                  </p>
                </div>
              </div>
            </section>
          </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
