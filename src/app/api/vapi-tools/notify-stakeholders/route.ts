import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tracking_number, stakeholder_types, message } = body;

    if (!tracking_number || !stakeholder_types || !message) {
      return NextResponse.json(
        { error: 'Tracking number, stakeholder types, and message are required' },
        { status: 400 }
      );
    }

    // Mock notification sending - in real app, this would send actual notifications
    const notifications = stakeholder_types.map((type: string) => ({
      id: `notification_${Date.now()}_${type}`,
      tracking_number,
      stakeholder_type: type,
      message,
      status: 'sent',
      sent_at: new Date().toISOString()
    }));

    const stakeholderNames = {
      consignee: 'recipient',
      warehouse: 'warehouse team',
      customer_service: 'customer service',
      customs_broker: 'customs broker',
      finance: 'finance department',
      manager: 'operations manager'
    };

    const notifiedStakeholders = stakeholder_types
      .map((type: string) => stakeholderNames[type as keyof typeof stakeholderNames] || type)
      .join(', ');

    return NextResponse.json({
      success: true,
      data: {
        tracking_number,
        notifications_sent: notifications.length,
        stakeholders_notified: stakeholder_types,
        notifications
      },
      voice_optimized_response: `I've successfully sent notifications about shipment ${tracking_number} to ${notifications.length} stakeholders including the ${notifiedStakeholders}. All parties have been informed with your message: "${message}"`
    });

  } catch (error) {
    console.error('VAPI tool error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}