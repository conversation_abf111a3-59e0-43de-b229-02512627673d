import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tracking_number, alert_type, message } = body;

    if (!tracking_number || !alert_type) {
      return NextResponse.json(
        { error: 'Tracking number and alert type are required' },
        { status: 400 }
      );
    }

    // Mock alert creation - in real app, this would create alerts in your system
    const alertData = {
      id: `alert_${Date.now()}`,
      tracking_number,
      alert_type,
      message: message || `${alert_type} alert created for shipment ${tracking_number}`,
      created_at: new Date().toISOString(),
      status: 'active'
    };

    const alertTypeMessages = {
      delay: `I've created a delay alert for shipment ${tracking_number}. The relevant stakeholders will be notified about potential delays.`,
      damage: `A damage alert has been created for shipment ${tracking_number}. This will trigger an immediate inspection and claim process.`,
      customs_hold: `I've set up a customs hold alert for shipment ${tracking_number}. The customs team will be notified to expedite clearance.`,
      delivery_attempt: `A delivery attempt alert has been created for shipment ${tracking_number}. The recipient will be notified to ensure availability.`
    };

    return NextResponse.json({
      success: true,
      data: alertData,
      voice_optimized_response: alertTypeMessages[alert_type as keyof typeof alertTypeMessages] || 
        `Alert created successfully for shipment ${tracking_number}.`
    });

  } catch (error) {
    console.error('VAPI tool error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}