import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { tracking_number } = body;

    if (!tracking_number) {
      return NextResponse.json(
        { error: 'Tracking number is required' },
        { status: 400 }
      );
    }

    // Mock shipment data - in real app, this would query your backend
    const mockShipmentData = {
      tracking_number,
      status: 'In Transit',
      location: 'Chicago, IL',
      expected_delivery: '2024-01-25',
      carrier: 'FedEx',
      last_update: '2024-01-20T14:30:00Z',
      progress_percentage: 75,
      voice_response: `Your shipment ${tracking_number} is currently in transit and located in Chicago, Illinois. It's 75% complete and expected to be delivered on January 25th, 2024 via FedEx. Everything is on schedule with no delays reported.`
    };

    return NextResponse.json({
      success: true,
      data: mockShipmentData,
      voice_optimized_response: mockShipmentData.voice_response
    });

  } catch (error) {
    console.error('VAPI tool error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}