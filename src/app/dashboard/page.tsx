"use client";

import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { MetricsCards } from "@/components/dashboard/metrics-cards";
import { TopCarriers } from "@/components/dashboard/top-carriers";
import { RecentActivity } from "@/components/dashboard/recent-activity";
import { VoiceAnalytics } from "@/components/dashboard/voice-analytics";
import { AlertTriangle, Search, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";

export default function DashboardPage() {
  const context = useOrganizationContext();
  const { fetchDashboardData, isLoading, error } = useDashboardStore();

  useEffect(() => {
    if (context?.apiClient) {
      fetchDashboardData(context.apiClient);
    }
  }, [context?.apiClient, fetchDashboardData]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Clean Header - BizLink Style */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">
                Dashboard
              </h1>
              <p className="text-sm text-gray-500 mt-1">
                Freight management overview
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                <Search className="h-4 w-4 mr-2" />
                Track shipment
              </Button>
              <Button
                size="sm"
                className="bg-gray-900 hover:bg-gray-800 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add shipment
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-8 space-y-8">

        {/* Status Banner - BizLink Style */}
        {!isLoading && !error && (
          <div className="bg-blue-50 border border-blue-100 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <p className="text-sm font-medium text-gray-700">
                  3 shipments need attention • Performance up 5% this week
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-600 hover:bg-blue-100 text-sm font-medium"
              >
                View details
              </Button>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-100 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <div>
                <p className="font-medium text-red-900">System Alert</p>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="h-4 w-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
              <p className="text-sm text-gray-600">Loading dashboard data...</p>
            </div>
          </div>
        )}

        {/* Main Dashboard Content - BizLink Style */}
        <div className="space-y-8">
          {/* Metrics Cards */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-6">
              Key Metrics
            </h2>
            <MetricsCards />
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Recent Activity and Top Carriers */}
            <div className="lg:col-span-2 space-y-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">
                  Recent Activity
                </h3>
                <RecentActivity />
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-6">
                  Top Carriers
                </h3>
                <TopCarriers />
              </div>
            </div>

            {/* Right Column - Voice Analytics */}
            <div className="lg:col-span-1">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Voice Intelligence
              </h3>
              <VoiceAnalytics />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
