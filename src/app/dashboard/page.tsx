"use client";

import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { MetricsCards } from "@/components/dashboard/metrics-cards";
import { TopCarriers } from "@/components/dashboard/top-carriers";
import { RecentActivity } from "@/components/dashboard/recent-activity";
import { VoiceAnalytics } from "@/components/dashboard/voice-analytics";
import { FloatingActionButton } from "@/components/dashboard/floating-action-button";
import { AlertTriangle, TrendingUp, Search, Plus, Sparkles, Zap, Activity } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { useEffect } from "react";

export default function DashboardPage() {
  const context = useOrganizationContext();
  const { fetchDashboardData, isLoading, error } = useDashboardStore();

  useEffect(() => {
    if (context?.apiClient) {
      fetchDashboardData(context.apiClient);
    }
  }, [context?.apiClient, fetchDashboardData]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Modern Header with Glassmorphism */}
      <div className="glass-card border-0 border-b border-white/20 backdrop-blur-xl bg-white/80 dark:bg-slate-900/80">
        <div className="px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <div className="flex items-center gap-3">
                <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-500 bg-clip-text text-transparent">
                  Dashboard
                </h1>
                <Badge className="bg-gradient-to-r from-blue-500 to-teal-500 text-white border-0 px-3 py-1">
                  <Sparkles className="h-3 w-3 mr-1" />
                  Live
                </Badge>
              </div>
              <p className="text-sm text-slate-600 dark:text-slate-400 font-medium">
                Real-time freight management & analytics
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="glass-card border-white/30 hover:bg-white/50 dark:hover:bg-slate-800/50 transition-all duration-300"
              >
                <Search className="h-4 w-4 mr-2" />
                Quick Track
              </Button>
              <Button
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-teal-500 hover:from-blue-700 hover:to-teal-600 border-0 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Shipment
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-8">

        {/* AI-Powered Quick Insights */}
        {!isLoading && !error && (
          <div className="glass-card p-6 bg-gradient-to-r from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/50 dark:border-blue-800/50 rounded-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl">
                  <Activity className="h-5 w-5 text-white" />
                </div>
                <div>
                  <span className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                    AI Insights
                  </span>
                  <p className="text-xs text-slate-600 dark:text-slate-400">
                    3 shipments need attention • Performance up 5% this week
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-600 dark:text-blue-400 hover:bg-blue-100/50 dark:hover:bg-blue-900/30 rounded-xl"
              >
                <Zap className="h-4 w-4 mr-1" />
                View Details
              </Button>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="glass-card p-4 bg-gradient-to-r from-red-50/80 to-orange-50/80 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200/50 dark:border-red-800/50 rounded-2xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl">
                <AlertTriangle className="h-5 w-5 text-white" />
              </div>
              <div>
                <p className="font-medium text-red-700 dark:text-red-300">System Alert</p>
                <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="glass-card p-6 bg-gradient-to-r from-slate-50/80 to-gray-50/80 dark:from-slate-800/50 dark:to-gray-800/50 border border-slate-200/50 dark:border-slate-700/50 rounded-2xl">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-r from-slate-400 to-gray-400 rounded-xl">
                <div className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
              </div>
              <div>
                <p className="font-medium text-slate-700 dark:text-slate-300">Loading Dashboard</p>
                <p className="text-sm text-slate-600 dark:text-slate-400">Fetching real-time data...</p>
              </div>
            </div>
          </div>
        )}

        {/* Main Dashboard Content */}
        <div className="space-y-8">
          {/* Enhanced Metrics Cards Section */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold text-slate-800 dark:text-slate-200">
                Key Metrics
              </h2>
              <Separator className="flex-1 ml-4 bg-gradient-to-r from-transparent via-slate-300 to-transparent dark:via-slate-600" />
            </div>
            <div className="w-full">
              <MetricsCards />
            </div>
          </div>

          {/* Enhanced Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Recent Activity and Top Carriers */}
            <div className="lg:col-span-2 space-y-8">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                  <div className="w-2 h-2 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full"></div>
                  Recent Activity
                </h3>
                <RecentActivity />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                  <div className="w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full"></div>
                  Top Carriers
                </h3>
                <TopCarriers />
              </div>
            </div>

            {/* Right Column - Voice Analytics */}
            <div className="lg:col-span-1 space-y-8">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 flex items-center gap-2">
                  <div className="w-2 h-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
                  Voice Intelligence
                </h3>
                <VoiceAnalytics />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Button */}
      <FloatingActionButton />
    </div>
  );
}
