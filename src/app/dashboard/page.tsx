"use client";

import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { MetricsCards } from "@/components/dashboard/metrics-cards";
import { TopCarriers } from "@/components/dashboard/top-carriers";
import { RecentActivity } from "@/components/dashboard/recent-activity";
import { VoiceAnalytics } from "@/components/dashboard/voice-analytics";
import { AlertTriangle, TrendingUp, Search, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";

export default function DashboardPage() {
  const context = useOrganizationContext();
  const { fetchDashboardData, isLoading, error } = useDashboardStore();

  useEffect(() => {
    if (context?.apiClient) {
      fetchDashboardData(context.apiClient);
    }
  }, [context?.apiClient, fetchDashboardData]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Clean Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
              <p className="text-sm text-gray-500 mt-0.5">Freight management overview</p>
            </div>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Search className="h-4 w-4 mr-1" />
                Track
              </Button>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-1" />
                New
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-4">

        {/* Quick Insights */}
        {!isLoading && !error && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-100 rounded-md">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-gray-700">
                  3 shipments need attention • Performance up 5%
                </span>
              </div>
              <Button variant="ghost" size="sm" className="text-blue-600 h-7">
                View
              </Button>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-100 rounded-md flex items-center gap-2">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="mb-4 p-3 bg-gray-50 border border-gray-100 rounded-md flex items-center gap-2">
            <div className="h-4 w-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin" />
            <p className="text-gray-600 text-sm">Loading dashboard data...</p>
          </div>
        )}

        {/* Main Dashboard Content */}
        <div className="space-y-6">
          {/* Metrics Cards - Full Width Grid */}
          <div className="w-full">
            <MetricsCards />
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Recent Activity and Top Carriers */}
            <div className="lg:col-span-2 space-y-6">
              <RecentActivity />
              <TopCarriers />
            </div>

            {/* Right Column - Voice Analytics */}
            <div className="lg:col-span-1 space-y-6">
              <VoiceAnalytics />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
