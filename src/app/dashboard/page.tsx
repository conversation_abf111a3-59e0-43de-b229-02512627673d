"use client";

import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { MetricsCards } from "@/components/dashboard/metrics-cards";
import { TopCarriers } from "@/components/dashboard/top-carriers";
import { RecentActivity } from "@/components/dashboard/recent-activity";
import { VoiceAnalytics } from "@/components/dashboard/voice-analytics";
import { AlertTriangle, Search, Plus, Activity } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";

export default function DashboardPage() {
  const context = useOrganizationContext();
  const { fetchDashboardData, isLoading, error } = useDashboardStore();

  useEffect(() => {
    if (context?.apiClient) {
      fetchDashboardData(context.apiClient);
    }
  }, [context?.apiClient, fetchDashboardData]);

  return (
    <div className="min-h-screen bg-slate-50 dark:bg-slate-900">
      {/* Clean Professional Header */}
      <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
        <div className="px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
                Dashboard
              </h1>
              <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                Freight management overview
              </p>
            </div>
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                className="border-slate-300 dark:border-slate-600"
              >
                <Search className="h-4 w-4 mr-2" />
                Track
              </Button>
              <Button
                size="sm"
                className="bg-slate-900 hover:bg-slate-800 dark:bg-slate-100 dark:hover:bg-slate-200 dark:text-slate-900"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Shipment
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">

        {/* Quick Insights */}
        {!isLoading && !error && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-blue-600 rounded-lg">
                  <Activity className="h-4 w-4 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-900 dark:text-slate-100">
                    3 shipments need attention • Performance up 5% this week
                  </p>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="text-blue-600 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30"
              >
                View Details
              </Button>
            </div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-red-600 rounded-lg">
                <AlertTriangle className="h-4 w-4 text-white" />
              </div>
              <div>
                <p className="font-medium text-red-900 dark:text-red-100">System Alert</p>
                <p className="text-sm text-red-700 dark:text-red-300">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {isLoading && (
          <div className="bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="h-4 w-4 border-2 border-slate-300 border-t-slate-600 rounded-full animate-spin" />
              <p className="text-sm text-slate-600 dark:text-slate-400">Loading dashboard data...</p>
            </div>
          </div>
        )}

        {/* Main Dashboard Content */}
        <div className="space-y-6">
          {/* Metrics Cards */}
          <div>
            <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
              Key Metrics
            </h2>
            <MetricsCards />
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Recent Activity and Top Carriers */}
            <div className="lg:col-span-2 space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
                  Recent Activity
                </h3>
                <RecentActivity />
              </div>

              <div>
                <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
                  Top Carriers
                </h3>
                <TopCarriers />
              </div>
            </div>

            {/* Right Column - Voice Analytics */}
            <div className="lg:col-span-1">
              <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
                Voice Intelligence
              </h3>
              <VoiceAnalytics />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
