// src/app/dashboard/layout.tsx
"use client";

import { useRouter } from "next/navigation";
import { Head<PERSON> } from "@/components/layout/header";
import { Sidebar } from "@/components/layout/sidebar";
import { OrganizationSelector } from "@/components/organization/OrganizationSelector";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useAuth } from "@/contexts/AuthContext";
import { ArrowLeft, Building, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const context = useOrganizationContext();
  const router = useRouter();
  
  // Always call hooks first - before any conditional returns
  const { user, isLoading: authLoading } = useAuth();

  // Loading state - context is initializing from localStorage
  if (!context) {
    return (
      <div className="flex h-screen items-center justify-center bg-background">
        <div className="animate-pulse flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg font-medium">Loading PACE Dashboard...</p>
        </div>
      </div>
    );
  }

  // Organization selection state - context exists but no organization is selected
  if (!context.organizationId) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 flex flex-col">
        {/* Simple header for organization selection screen */}
        <header className="border-b bg-white/90 backdrop-blur-xl">
          <div className="container mx-auto py-4 px-6 flex items-center justify-between">
            <h1 className="text-xl font-bold flex items-center gap-2">
              <Building className="h-5 w-5 bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent" /> 
              <span className="bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">PACE Dashboard</span>
            </h1>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={() => router.push('/')}
              className="flex items-center gap-1 text-blue-600 hover:text-teal-600 rounded-xl"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Home
            </Button>
          </div>
        </header>

        <div className="flex-1 flex items-center justify-center p-4">
          <div className="w-full max-w-md">
            <OrganizationSelector />
            <p className="text-center text-xs text-muted-foreground mt-4">
              Select an organization to access the dashboard. If you don&apos;t have an
              organization ID, please contact your administrator.
            </p>
          </div>
        </div>

        {/* Simple footer */}
        <footer className="py-4 border-t bg-background/50 backdrop-blur-sm">
          <div className="container mx-auto px-6">
            <p className="text-center text-sm text-muted-foreground">
              &copy; PACE {new Date().getFullYear()} - Streamlining Freight
              Management
            </p>
          </div>
        </footer>
      </div>
    );
  }

  
  // Main dashboard layout - organization is selected
  // Show loading state while authentication is being determined
  if (authLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-background">
        <div className="animate-pulse flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-lg font-medium">Loading authentication...</p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="flex h-screen bg-gray-50">
      <div className="hidden md:flex md:w-64 md:flex-col">
        <Sidebar />
      </div>
      <div className="flex flex-col flex-1">
        <Header user={user || { email: "" }} />
        <main className="flex-1 overflow-auto pb-6">{children}</main>
      </div>
    </div>
  );
}
