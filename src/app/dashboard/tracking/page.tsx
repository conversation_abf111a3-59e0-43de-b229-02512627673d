"use client";

import { useState, useEffect, useCallback } from "react";
import { getDisplayLocation } from "@/lib/location-utils";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Search, MapPin, Clock, Package, RefreshCcw, AlertCircle, X, Shield, Mic, Eye } from "lucide-react";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useAuth } from "@/contexts/AuthContext";
import { format } from "date-fns";
import { trackFeatureUsage, trackPageView } from "@/lib/analytics";

import { Shipment } from "@/lib/types";

// Tracking interfaces
interface TrackingUpdate {
  timestamp: string;
  location: string; 
  status: string;
  details: string;
}

interface TrackingDetails {
  trackingNumber: string;
  carrier: string;
  status: string;
  origin: string;
  destination: string;
  estimatedDelivery: string | null;
  updates: TrackingUpdate[];
}

interface ApiTrackingUpdate {
  timestamp?: string;
  date?: string;
  status_description?: string; 
  status?: string;
  notes?: string;
  description?: string;
  carrier?: string;
  carrier_name?: string;
  [key: string]: unknown;
}

export default function TrackingPage() {
  const { 
    recentShipments: shipments, 
    isLoading, 
    fetchShipments 
  } = useDashboardStore();
  const context = useOrganizationContext();
  const apiClient = context?.apiClient;
  const { user, hasRole, hasPermission, canAccessWithoutPIN } = useAuth();

  const [trackingQuery, setTrackingQuery] = useState("");
  const [searchResults, setSearchResults] = useState<Shipment[]>([]);
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);
  const [trackingDetails, setTrackingDetails] = useState<TrackingDetails | null>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const stableFetchShipments = useCallback(() => {
    if (apiClient) {
      fetchShipments(apiClient);
    }
  }, [apiClient, fetchShipments]);

  useEffect(() => {
    stableFetchShipments();
  }, [stableFetchShipments]);

  // Track page view on mount
  useEffect(() => {
    trackPageView('Tracking Page', {
      userRole: user?.role || 'unknown'
    });
  }, [user?.role]); 

  const handleRefresh = () => {
    // Track refresh action
    trackFeatureUsage('Refresh Tracking', {
      userRole: user?.role || 'unknown',
      hasActiveSelection: !!selectedShipment
    });

    stableFetchShipments();
    if (selectedShipment) {
      fetchTrackingDetails(selectedShipment.tracking_number);
    }
  };

  const handleQuickTrack = () => {
    setError(null);
    if (!trackingQuery.trim()) {
      setError("Please enter a tracking number or reference");
      return;
    }

    // Track search attempt
    trackFeatureUsage('Quick Track Search', {
      userRole: user?.role || 'unknown',
      query: trackingQuery,
      queryType: trackingQuery.toUpperCase().startsWith('TRK') ? 'tracking_number' : 'reference'
    });
    
    // Filter shipments based on tracking number or reference
    const results = Array.isArray(shipments) 
      ? shipments.filter(shipment => 
          shipment && (
            shipment.tracking_number?.toLowerCase().includes(trackingQuery.toLowerCase()) ||
            shipment.external_reference_number?.toLowerCase().includes(trackingQuery.toLowerCase())
          )
        )
      : [];
    
    setSearchResults(results);
    
    if (results.length === 0) {
      setError("No shipments found matching your search");
    } else if (results.length === 1) {
      // If only one result, automatically select it
      handleSelectShipment(results[0]);
    } else {
      // Track found multiple results
      trackFeatureUsage('Quick Track Results', {
        userRole: user?.role || 'unknown',
        resultsCount: results.length
      });
    }
  };

  // Handle shipment selection with analytics
  const handleSelectShipment = (shipment: Shipment) => {
    setSelectedShipment(shipment);
    fetchTrackingDetails(shipment.tracking_number);

    // Track shipment selection
    trackFeatureUsage('Select Shipment', {
      userRole: user?.role || 'unknown',
      trackingNumber: shipment.tracking_number,
      status: shipment.status,
      origin: shipment.origin_address || '',
      destination: shipment.destination_address || ''
    });
  };

  const fetchTrackingDetails = async (trackingNumber: string) => {
    if (!apiClient) {
      setError("API client not available. Please check your organization context.");
      return;
    }

    setIsLoadingDetails(true);
    setError(null);
    
    try {
      console.log(`🔍 Fetching tracking details for: ${trackingNumber}`);
      
      // Try enhanced tracking API first, then basic tracking
      let trackingResponse;
      try {
        trackingResponse = await apiClient.getEnhancedTracking(trackingNumber);
        console.log(`✅ Enhanced tracking data:`, trackingResponse);
      } catch (enhancedError) {
        console.log(`⚠️ Enhanced tracking failed, trying basic tracking:`, enhancedError);
        trackingResponse = await apiClient.getBasicTracking(trackingNumber);
        console.log(`✅ Basic tracking data:`, trackingResponse);
      }
      
      const trackingData = trackingResponse.data || trackingResponse;
      
      // Process the tracking data
      const details: TrackingDetails = {
        trackingNumber: trackingNumber,
        carrier: trackingData.carrier?.name || trackingData.carrier || "Unknown",
        status: trackingData.status || "Unknown",
        origin: trackingData.origin || "Unknown", 
        destination: trackingData.destination || "Unknown",
        estimatedDelivery: trackingData.estimated_delivery || trackingData.estimatedDelivery || null,
        
        updates: Array.isArray(trackingData.history) ? trackingData.history.map((update: ApiTrackingUpdate) => ({
          timestamp: update.timestamp || update.date,
          status: update.status_description || update.status,
          location: getDisplayLocation(update),
          details: update.notes || update.description || ""
        })) : []
      };
      
      setTrackingDetails(details);
      
    } catch (apiError: unknown) {
      const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown error occurred';
      console.error(`❌ Failed to fetch tracking details for ${trackingNumber}:`, errorMessage);
      setError(`Failed to load tracking details: ${errorMessage}`);
      setTrackingDetails(null);
    } finally {
      setIsLoadingDetails(false);
    }
  };
  

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "delivered":
        return "bg-green-500";
      case "in transit":
        return "bg-blue-500";
      case "delayed":
        return "bg-amber-500";
      case "exception":
        return "bg-red-500";
      case "pending":
      case "shipped":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "delivered":
        return <Package className="h-4 w-4" />;
      case "in transit":
        return <MapPin className="h-4 w-4" />;
      case "delayed":
      case "exception":
        return <Clock className="h-4 w-4" />;
      default:
        return <Package className="h-4 w-4" />;
    }
  };

  // Get role-based content
  const getRoleBasedContent = () => {
    if (!user) return { 
      title: "Tracking", 
      description: "Track shipments in real-time and monitor status updates",
      capabilities: []
    };
    
    switch (user.role) {
      case 'ADMIN':
        return { 
          title: "System-Wide Tracking", 
          description: "Real-time tracking visibility across all organizations and shipments",
          capabilities: ["Global visibility", "Voice tracking", "Cross-org monitoring", "Advanced analytics"]
        };
      case 'BROKER':
        return { 
          title: "Brokerage Tracking", 
          description: "Track and monitor all shipments under your brokerage management",
          capabilities: ["Managed shipments", "Carrier coordination", "Voice updates", "Client notifications"]
        };
      case 'CARRIER':
        return { 
          title: "Route Tracking", 
          description: "Monitor your assigned routes and update delivery status in real-time",
          capabilities: ["Route updates", "Voice reporting", "GPS integration", "Delivery confirmation"]
        };
      case 'CONSIGNOR':
        return { 
          title: "Shipment Tracking", 
          description: "Track your shipped packages and monitor delivery progress",
          capabilities: ["Your shipments", "Delivery alerts", "Status monitoring", "ETA updates"]
        };
      case 'CONSIGNEE':
        return { 
          title: "Delivery Tracking", 
          description: "Monitor incoming packages and track delivery timelines",
          capabilities: ["Incoming packages", "Delivery scheduling", "Receipt confirmation", "Real-time updates"]
        };
      default:
        return { 
          title: "Tracking", 
          description: "Track shipments in real-time and monitor status updates",
          capabilities: []
        };
    }
  };

  const roleContent = getRoleBasedContent();

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">{roleContent.title}</h1>
            {user && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  {user.role}
                </Badge>
                {canAccessWithoutPIN('view_tracking') && (
                  <Badge variant="outline" className="flex items-center gap-1 text-green-600 border-green-600">
                    <Eye className="h-3 w-3" />
                    Instant Tracking
                  </Badge>
                )}
                {hasPermission('voice_operations') && (
                  <Badge variant="outline" className="flex items-center gap-1 text-blue-600 border-blue-600">
                    <Mic className="h-3 w-3" />
                    Voice Enhanced
                  </Badge>
                )}
              </div>
            )}
          </div>
          <p className="text-muted-foreground mt-1">{roleContent.description}</p>
          {roleContent.capabilities.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {roleContent.capabilities.map((capability, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {capability}
                </Badge>
              ))}
            </div>
          )}
        </div>
        <Button 
          onClick={handleRefresh} 
          variant="outline" 
          size="sm" 
          className="gap-1"
          disabled={isLoading}
        >
          <RefreshCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          {isLoading ? 'Loading...' : 'Refresh Data'}
        </Button>
      </div>

      {error && (
        <div className="bg-destructive/15 border border-destructive/30 text-destructive rounded-md p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            <p>{error}</p>
          </div>
        </div>
      )}

      {/* Quick Search Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>
                {hasRole('CARRIER') ? 'Route Quick Track' :
                 hasRole('CONSIGNEE') ? 'Delivery Quick Track' :
                 hasRole('ADMIN') ? 'System-Wide Quick Track' : 'Quick Track'}
              </CardTitle>
              <CardDescription>
                {hasRole('CARRIER') ? 'Enter tracking number to access route information and update delivery status' :
                 hasRole('CONSIGNEE') ? 'Track your incoming deliveries and confirm receipt status' :
                 hasRole('ADMIN') ? 'Search any shipment across all organizations with enhanced access' :
                 'Enter a tracking number or reference to quickly find shipment details'}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {hasPermission('voice_operations') && (
                <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                  <Mic className="h-3 w-3 mr-1" />
                  Voice Search
                </Badge>
              )}
              {canAccessWithoutPIN('view_tracking') && (
                <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                  PIN-Free
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder={
                  hasRole('CARRIER') ? "Enter route tracking number..." :
                  hasRole('CONSIGNEE') ? "Enter your delivery tracking number..." :
                  hasRole('ADMIN') ? "Enter any tracking number (system-wide search)..." :
                  "Enter tracking number or reference..."
                }
                className="pl-8"
                value={trackingQuery}
                onChange={(e) => setTrackingQuery(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleQuickTrack()}
              />
            </div>
            <div className="flex gap-2">
              {hasPermission('voice_operations') && (
                <Button 
                  variant="outline" 
                  onClick={() => alert("Voice tracking search coming soon!")}
                  className="gap-1"
                >
                  <Mic className="h-4 w-4" />
                  Voice
                </Button>
              )}
              <Button onClick={handleQuickTrack} disabled={!trackingQuery.trim()}>
                {hasRole('CARRIER') ? 'Track Route' :
                 hasRole('CONSIGNEE') ? 'Track Delivery' :
                 hasRole('ADMIN') ? 'System Search' : 'Track'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      {searchResults.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>
                  {hasRole('CARRIER') ? 'Route Search Results' :
                   hasRole('CONSIGNEE') ? 'Delivery Search Results' :
                   hasRole('ADMIN') ? 'System Search Results' : 'Search Results'}
                </CardTitle>
                <CardDescription>
                  Found {searchResults.length} shipment(s) matching your query
                  {hasRole('ADMIN') && ' (system-wide search)'}
                  {hasRole('CARRIER') && ' (your assigned routes)'}
                  {hasRole('CONSIGNEE') && ' (your deliveries)'}
                </CardDescription>
              </div>
              {canAccessWithoutPIN('view_tracking') && (
                <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                  Instant Access
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-auto rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tracking Number</TableHead>
                    <TableHead>Reference</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Origin</TableHead>
                    <TableHead>Destination</TableHead>
                    <TableHead>Last Update</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {searchResults.map((shipment) => (
                    <TableRow 
                      key={shipment.id} 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => handleSelectShipment(shipment)}
                    >
                      <TableCell className="font-medium">
                        {shipment.tracking_number}
                      </TableCell>
                      <TableCell>
                        {shipment.external_reference_number || "N/A"}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(shipment.status)}>
                          <div className="flex items-center gap-1">
                            {getStatusIcon(shipment.status)}
                            {shipment.status}
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell>{shipment.origin_address || "N/A"}</TableCell>
                      <TableCell>{shipment.destination_address || "N/A"}</TableCell>
                      <TableCell>
                        {shipment.updated_at
                          ? format(new Date(shipment.updated_at), "MMM dd, HH:mm")
                          : "N/A"}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Real-time Monitoring */}
      <Card>
        <CardHeader>
          <CardTitle>Active Shipments</CardTitle>
          <CardDescription>
            Monitor all active shipments with real-time status updates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-auto rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Tracking Number</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Current Location</TableHead>
                  <TableHead>Expected Delivery</TableHead>
                  <TableHead>Progress</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(3).fill(0).map((_, idx) => (
                    <TableRow key={idx}>
                      <TableCell colSpan={5}>
                        <div className="h-6 w-full animate-pulse bg-muted rounded-md"></div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : Array.isArray(shipments) && shipments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6 text-muted-foreground">
                      No active shipments found
                    </TableCell>
                  </TableRow>
                ) : (
                  Array.isArray(shipments) && shipments
                    .filter(s => s && s.status && !['delivered', 'cancelled'].includes(s.status.toLowerCase()))
                    .slice(0, 10)
                    .map((shipment) => (
                      <TableRow key={shipment.id}>
                        <TableCell className="font-medium">
                          {shipment.tracking_number}
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(shipment.status)}>
                            <div className="flex items-center gap-1">
                              {getStatusIcon(shipment.status)}
                              {shipment.status}
                            </div>
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {shipment.origin_address || "In Transit"}
                        </TableCell>
                        <TableCell>
                          {shipment.expected_delivery_date 
                            ? format(new Date(shipment.expected_delivery_date), "MMM dd, yyyy")
                            : "TBD"}
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="w-24 bg-muted rounded-full h-2">
                              <div 
                                className="bg-primary h-2 rounded-full transition-all"
                                style={{ 
                                  width: shipment.status?.toLowerCase() === 'delivered' ? '100%' : 
                                        shipment.status?.toLowerCase() === 'in transit' ? '60%' : '30%' 
                                }}
                              />
                            </div>
                            <span className="text-xs text-muted-foreground">
                              {shipment.status?.toLowerCase() === 'delivered' ? '100%' : 
                               shipment.status?.toLowerCase() === 'in transit' ? '60%' : '30%'}
                            </span>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Tracking Details - Slide Over */}
      {selectedShipment && trackingDetails && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="w-full max-w-2xl rounded-lg bg-white shadow-lg">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-semibold">
                Tracking Details - {selectedShipment.tracking_number}
              </h2>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => setSelectedShipment(null)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <div className="p-4">
              {isLoadingDetails ? (
                <div className="animate-pulse">
                  <div className="h-4 bg-muted rounded-md mb-2"></div>
                  <div className="h-4 bg-muted rounded-md mb-2"></div>
                  <div className="h-4 bg-muted rounded-md"></div>
                </div>
              ) : error ? (
                <div className="text-destructive">
                  <AlertCircle className="inline h-5 w-5 mr-2" />
                  {error}
                </div>
              ) : (
                <div>
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <span className="text-sm text-muted-foreground">Carrier</span>
                      <h3 className="text-lg font-semibold">
                        {trackingDetails.carrier}
                      </h3>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Status</span>
                      <Badge className={getStatusColor(trackingDetails.status)}>
                        <div className="flex items-center gap-1">
                          {getStatusIcon(trackingDetails.status)}
                          {trackingDetails.status}
                        </div>
                      </Badge>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Origin</span>
                      <h3 className="text-lg font-semibold">
                        {trackingDetails.origin}
                      </h3>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Destination</span>
                      <h3 className="text-lg font-semibold">
                        {trackingDetails.destination}
                      </h3>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">Estimated Delivery</span>
                      <h3 className="text-lg font-semibold">
                        {trackingDetails.estimatedDelivery 
                          ? format(new Date(trackingDetails.estimatedDelivery), "MMM dd, yyyy")
                          : "TBD"}
                      </h3>
                    </div>
                  </div>

                  <div className="mb-4">
                    <span className="text-sm text-muted-foreground">Tracking Updates</span>
                    <div className="space-y-2">
                      {trackingDetails.updates.map((update, idx) => (
                        <div key={idx} className="p-3 rounded-md bg-muted">
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>{format(new Date(update.timestamp), "MMM dd, HH:mm")}</span>
                            <span>{update.location}</span>
                          </div>
                          <div className="mt-1">
                            <span className="text-sm font-semibold">{update.status}</span>
                            <p className="text-sm text-muted-foreground">
                              {update.details}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

