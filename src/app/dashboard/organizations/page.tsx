"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
// Remove unused Select imports
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Organization, User } from "@/lib/types";
import { format } from "date-fns";
import { 
  MoreHorizontal, 
  Plus, 
  Search, 
  Building, 
  Mail, 
  RefreshCcw, 
  AlertCircle,
  Shield,
  Mic,
  CheckCircle,
  Eye,
  Users,
  Settings,
  Truck
} from "lucide-react";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useAuth } from "@/contexts/AuthContext";

export default function OrganizationsPage() {
  const { user, isAuthenticated, hasRole, canAccessWithoutPIN } = useAuth();
  const { 
    organizations,
    isLoadingOrganizations: isLoading,
    fetchAllOrganizations,
    apiClient
  } = useOrganizationContext();
  const [error, setError] = useState("");

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedOrg, setSelectedOrg] = useState<Organization | null>(null);
  const [isPinRequired, setIsPinRequired] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [filterByType, setFilterByType] = useState<string>("all");
  const [isExporting, setIsExporting] = useState(false);
  const [voiceActive, setVoiceActive] = useState(false);

  // Role-based content configuration
  const getRoleBasedConfig = () => {
    if (hasRole('ADMIN') || hasRole('BROKER')) {
      return {
        title: "Organization Management Hub",
        description: "Comprehensive organization oversight with advanced analytics and management capabilities",
        capabilities: ['view_all', 'create', 'edit', 'delete', 'manage_users', 'bulk_operations', 'export_data', 'voice_commands'],
        filterTypes: ['all', 'carrier', 'consignor', 'consignee', 'broker', 'inactive'],
        showAdvancedFeatures: true
      };
    } else if (hasRole('CARRIER') || hasRole('CONSIGNOR') || hasRole('CONSIGNEE')) {
      return {
        title: "Network Organizations",
        description: "View and connect with organizations in your shipping network",
        capabilities: ['view_network', 'contact_info', 'export_contacts'],
        filterTypes: ['all', 'carrier', 'consignor', 'consignee'],
        showAdvancedFeatures: false
      };
    } else {
      return {
        title: "Organizations Directory",
        description: "Browse available organizations in the system",
        capabilities: ['view_basic'],
        filterTypes: ['all'],
        showAdvancedFeatures: false
      };
    }
  };

  const config = getRoleBasedConfig();

  // Check user capabilities
  useEffect(() => {
    const checkCapabilities = async () => {
      if (user?.id && apiClient) {
        try {
          // Check PIN requirements for viewing organizations
          const response = await apiClient.post('/auth/check-pin-requirement', {
            action: 'VIEW_ORGANIZATIONS',
            context: { user_id: user.id }
          }, false);
          setIsPinRequired(!response.instantAccess);
          
          // Check voice capabilities
          setVoiceEnabled(config.capabilities.includes('voice_commands') && 
                         (user.permissions?.includes('VOICE_OPERATIONS') || false));
        } catch (error) {
          console.error('Error checking capabilities:', error);
          setIsPinRequired(true);
        }
      }
    };

    if (isAuthenticated && user && apiClient) {
      checkCapabilities();
    }
  }, [user, isAuthenticated, apiClient, config.capabilities]);

  const stableFetchOrganizations = useCallback(async () => {
    try {
      setError("");
      await fetchAllOrganizations();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch organizations');
    }
  }, [fetchAllOrganizations]);

  useEffect(() => {
    stableFetchOrganizations();
  }, [stableFetchOrganizations]);

  const handleRefresh = () => {
    stableFetchOrganizations();
  };

  // Enhanced export functionality with role-based permissions
  const handleExport = async () => {
    if (!config.capabilities.includes('export_data') && !config.capabilities.includes('export_contacts')) {
      alert('Export feature not available for your role');
      return;
    }

    const requiresPIN = !canAccessWithoutPIN('EXPORT_ORGANIZATIONS');
    if (requiresPIN && isPinRequired) {
      // PIN verification would be handled here
      alert('PIN verification required for export');
      return;
    }

    setIsExporting(true);
    try {
      const exportData = filteredOrganizations.map(org => ({
        name: org.name,
        type: getOrgTypeFromUsers(org),
        userCount: org.users?.length || 0,
        primaryContact: org.users?.[0]?.email || 'N/A',
        created: format(new Date(org.created_at), 'yyyy-MM-dd'),
        ...((hasRole('ADMIN') || hasRole('BROKER')) && config.showAdvancedFeatures && {
          id: org.id,
          updated: format(new Date(org.updated_at), 'yyyy-MM-dd'),
          allUsers: org.users?.map(u => u.email).join('; ') || ''
        })
      }));

      // Convert to CSV and download
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => headers.map(header => `"${row[header as keyof typeof row]}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `organizations-export-${format(new Date(), 'yyyy-MM-dd')}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Voice operations integration
  const handleVoiceCommand = () => {
    if (!voiceEnabled) {
      alert('Voice operations not available for your role');
      return;
    }

    setVoiceActive(true);
    // Voice command simulation
    setTimeout(() => {
      setVoiceActive(false);
      alert('Voice command processed: "Show carrier organizations"');
      setFilterByType('carrier');
    }, 2000);
  };

  // Enhanced filtering with role-based types
  const filteredOrganizations = Array.isArray(organizations) 
    ? organizations.filter(org => {
        if (!org || !org.name) return false;
        
        const matchesSearch = org.name.toLowerCase().includes(searchTerm.toLowerCase());
        
        if (filterByType === 'all') return matchesSearch;
        if (filterByType === 'inactive') {
          return matchesSearch && (!org.users || org.users.length === 0);
        }
        
        const orgType = getOrgTypeFromUsers(org).toLowerCase();
        return matchesSearch && orgType === filterByType;
      })
    : [];

  // Determine organization type based on its users' roles
  const getOrgTypeFromUsers = (org: Organization) => {
    if (!org.users || org.users.length === 0) {
      return "unknown";
    }
    
    // Get the most common role among users
    const roleCount: Record<string, number> = {};
    org.users.forEach(user => {
      if (user.role) {
        roleCount[user.role] = (roleCount[user.role] || 0) + 1;
      }
    });
    
    // Find the most common role
    let maxCount = 0;
    let dominantRole = "unknown";
    
    Object.entries(roleCount).forEach(([role, count]) => {
      if (count > maxCount) {
        maxCount = count;
        dominantRole = role;
      }
    });
    
    return dominantRole;
  };
  
  const getOrgTypeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "carrier":
        return "bg-blue-500";
      case "consignor":
        return "bg-green-500";
      case "consignee":
        return "bg-purple-500";
      case "broker":
        return "bg-amber-500";
      case "admin":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  // Enhanced organization metrics
  const getOrganizationMetrics = () => {
    const totalOrgs = Array.isArray(organizations) ? organizations.length : 0;
    const activeOrgs = Array.isArray(organizations) ? organizations.filter(org => org.users && org.users.length > 0).length : 0;
    
    const typeBreakdown = Array.isArray(organizations) ? organizations.reduce((acc, org) => {
      const type = getOrgTypeFromUsers(org);
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) : {};

    return { totalOrgs, activeOrgs, typeBreakdown };
  };

  const metrics = getOrganizationMetrics();

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Building className="h-8 w-8" />
            {config.title}
            {voiceEnabled && (
              <div className="flex items-center gap-1 ml-2">
                <Badge variant="outline" className="text-xs">
                  <Mic className="h-3 w-3 mr-1" />
                  Voice Enabled
                </Badge>
              </div>
            )}
          </h1>
          <p className="text-muted-foreground">{config.description}</p>
        </div>
        <div className="flex items-center gap-2">
          {voiceEnabled && (
            <Button 
              onClick={handleVoiceCommand}
              variant="outline" 
              size="sm" 
              className={`gap-2 ${voiceActive ? 'bg-blue-50 border-blue-300' : ''}`}
              disabled={voiceActive}
            >
              <Mic className={`h-4 w-4 ${voiceActive ? 'animate-pulse text-blue-600' : ''}`} />
              {voiceActive ? 'Listening...' : 'Voice Command'}
            </Button>
          )}
          {(config.capabilities.includes('export_data') || config.capabilities.includes('export_contacts')) && (
            <Button 
              onClick={handleExport}
              variant="outline" 
              size="sm" 
              className="gap-2"
              disabled={isExporting}
            >
              {isExporting ? (
                <RefreshCcw className="h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              {isExporting ? 'Exporting...' : 'Export'}
            </Button>
          )}
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            className="gap-1"
            disabled={isLoading}
          >
            <RefreshCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
          {config.capabilities.includes('create') && (
            <Button onClick={() => alert("Create organization functionality coming soon!")}>
              <Plus className="mr-2 h-4 w-4" /> New Organization
            </Button>
          )}
        </div>
      </div>

      {/* Role-based metrics cards */}
      {config.showAdvancedFeatures && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Organizations</p>
                  <p className="text-2xl font-bold">{metrics.totalOrgs}</p>
                </div>
                <Building className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Organizations</p>
                  <p className="text-2xl font-bold">{metrics.activeOrgs}</p>
                </div>
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Carriers</p>
                  <p className="text-2xl font-bold">{metrics.typeBreakdown.carrier || 0}</p>
                </div>
                <Truck className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Security Status</p>
                  <p className="text-lg font-bold flex items-center gap-1">
                    <Shield className="h-4 w-4 text-green-600" />
                    {isPinRequired ? 'PIN Required' : 'Verified'}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {error && (
        <div className="bg-destructive/15 border border-destructive/30 text-destructive rounded-md p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            <p><strong>Error:</strong> {error}</p>
          </div>
        </div>
      )}

      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                Organization Management
                {isPinRequired && (
                  <Badge variant="outline" className="text-xs">
                    <Shield className="h-3 w-3 mr-1" />
                    PIN Protected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {config.showAdvancedFeatures ? 
                  'Advanced organization management with comprehensive analytics' :
                  'View and interact with organizations in your network'
                }
              </CardDescription>
            </div>
            {config.showAdvancedFeatures && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Settings className="h-4 w-4" />
                Admin Mode
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search organizations..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            {config.filterTypes.length > 1 && (
              <div className="flex gap-2">
                {config.filterTypes.map((type) => (
                  <Button
                    key={type}
                    variant={filterByType === type ? "default" : "outline"}
                    size="sm"
                    onClick={() => setFilterByType(type)}
                    className="capitalize"
                  >
                    {type === 'all' ? 'All Types' : type}
                  </Button>
                ))}
              </div>
            )}
          </div>

          <div className="overflow-auto rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Organization Name</TableHead>
                  <TableHead>Primary Role</TableHead>
                  <TableHead>Primary Contact</TableHead>
                  <TableHead>Users</TableHead>
                  {config.showAdvancedFeatures && <TableHead>Status</TableHead>}
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(5).fill(0).map((_, idx) => (
                    <TableRow key={idx}>
                      <TableCell colSpan={config.showAdvancedFeatures ? 7 : 6}>
                        <div className="h-6 w-full animate-pulse bg-muted rounded-md"></div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredOrganizations.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={config.showAdvancedFeatures ? 7 : 6} className="text-center py-6 text-muted-foreground">
                      {filterByType === 'all' ? 'No organizations found' : `No ${filterByType} organizations found`}
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredOrganizations.map((org) => (
                    <TableRow key={org.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Building className="h-5 w-5 text-muted-foreground" />
                          <div>
                            {org.name}
                            {config.showAdvancedFeatures && org.id && (
                              <div className="text-xs text-muted-foreground">ID: {org.id}</div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {org.users && org.users.length > 0 ? (
                          <Badge className={getOrgTypeColor(getOrgTypeFromUsers(org))}>
                            {getOrgTypeFromUsers(org)}
                          </Badge>
                        ) : (
                          <Badge variant="outline">No users</Badge>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          {org.users && org.users.length > 0 ? (
                            <>
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span>{org.users[0].email || "N/A"}</span>
                              </div>
                              {org.users.length > 1 && (
                                <div className="text-xs text-muted-foreground mt-1">
                                  +{org.users.length - 1} more users
                                </div>
                              )}
                            </>
                          ) : (
                            <span className="text-muted-foreground">No contact info</span>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {org.users?.length || 0} users
                        </div>
                      </TableCell>
                      {config.showAdvancedFeatures && (
                        <TableCell>
                          <div className="flex items-center gap-1">
                            {(org.users?.length || 0) > 0 ? (
                              <>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                                <span className="text-sm text-green-600">Active</span>
                              </>
                            ) : (
                              <>
                                <AlertCircle className="h-4 w-4 text-amber-600" />
                                <span className="text-sm text-amber-600">Inactive</span>
                              </>
                            )}
                          </div>
                        </TableCell>
                      )}
                      <TableCell>
                        {format(new Date(org.created_at), "MMM dd, yyyy")}
                      </TableCell>
                      <TableCell className="text-right">
                        <Dialog>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                                <span className="sr-only">Open menu</span>
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => setSelectedOrg(org)}>
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              {config.capabilities.includes('edit') && (
                                <DropdownMenuItem>
                                  <Settings className="h-4 w-4 mr-2" />
                                  Edit Organization
                                </DropdownMenuItem>
                              )}
                              {config.capabilities.includes('manage_users') && (
                                <DropdownMenuItem>
                                  <Users className="h-4 w-4 mr-2" />
                                  Manage Users
                                </DropdownMenuItem>
                              )}
                              {config.capabilities.includes('export_data') && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem onClick={() => handleExport()}>
                                    <CheckCircle className="h-4 w-4 mr-2" />
                                    Export Data
                                  </DropdownMenuItem>
                                </>
                              )}
                              {config.capabilities.includes('delete') && (
                                <>
                                  <DropdownMenuSeparator />
                                  <DropdownMenuItem className="text-destructive">
                                    Delete Organization
                                  </DropdownMenuItem>
                                </>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                          
                          {selectedOrg && (
                            <DialogContent className="sm:max-w-2xl">
                              <DialogHeader>
                                <DialogTitle className="flex items-center gap-2">
                                  <Building className="h-5 w-5" />
                                  {selectedOrg.name}
                                  {config.showAdvancedFeatures && (
                                    <Badge variant="outline" className="ml-2">
                                      ID: {selectedOrg.id}
                                    </Badge>
                                  )}
                                </DialogTitle>
                                <DialogDescription>
                                  {config.showAdvancedFeatures ? 
                                    'Comprehensive organization details and management options' :
                                    'Organization details and associated users'
                                  }
                                </DialogDescription>
                              </DialogHeader>
                              
                              <div className="grid gap-4 py-4">
                                <div className="grid grid-cols-2 gap-4">
                                  <div>
                                    <h3 className="text-sm font-medium mb-1 flex items-center gap-1">
                                      <Shield className="h-4 w-4" />
                                      Organization Type
                                    </h3>
                                    {selectedOrg.users && selectedOrg.users.length > 0 ? (
                                      <Badge className={getOrgTypeColor(getOrgTypeFromUsers(selectedOrg))}>
                                        {getOrgTypeFromUsers(selectedOrg)}
                                      </Badge>
                                    ) : (
                                      <Badge variant="outline">No users</Badge>
                                    )}
                                  </div>
                                  <div>
                                    <h3 className="text-sm font-medium mb-1">Created</h3>
                                    <p className="text-sm">
                                      {format(new Date(selectedOrg.created_at), "MMM dd, yyyy")}
                                    </p>
                                  </div>
                                </div>
                                
                                {config.showAdvancedFeatures && (
                                  <div className="grid grid-cols-2 gap-4">
                                    <div>
                                      <h3 className="text-sm font-medium mb-1">Status</h3>
                                      <div className="flex items-center gap-1">
                                        {(selectedOrg.users?.length || 0) > 0 ? (
                                          <>
                                            <CheckCircle className="h-4 w-4 text-green-600" />
                                            <span className="text-sm text-green-600">Active</span>
                                          </>
                                        ) : (
                                          <>
                                            <AlertCircle className="h-4 w-4 text-amber-600" />
                                            <span className="text-sm text-amber-600">Inactive</span>
                                          </>
                                        )}
                                      </div>
                                    </div>
                                    <div>
                                      <h3 className="text-sm font-medium mb-1">Last Updated</h3>
                                      <p className="text-sm">
                                        {format(new Date(selectedOrg.updated_at), "MMM dd, yyyy")}
                                      </p>
                                    </div>
                                  </div>
                                )}
                                
                                <div>
                                  <h3 className="text-sm font-medium mb-2 flex items-center gap-1">
                                    <Users className="h-4 w-4" />
                                    Users ({selectedOrg.users?.length || 0})
                                  </h3>
                                  {selectedOrg.users && selectedOrg.users.length > 0 ? (
                                    <div className="space-y-2 max-h-48 overflow-y-auto">
                                      {selectedOrg.users.map((user: User) => (
                                        <div key={user.id} className="flex justify-between items-center p-3 bg-muted rounded-md">
                                          <div className="flex items-center gap-2">
                                            <div>
                                              <div className="font-medium">{user.full_name || user.email.split('@')[0]}</div>
                                              <div className="text-xs text-muted-foreground flex items-center gap-1">
                                                <Mail className="h-3 w-3" />
                                                {user.email}
                                              </div>
                                            </div>
                                          </div>
                                          <div className="flex items-center gap-2">
                                            <Badge variant="outline">{user.role}</Badge>
                                            {config.showAdvancedFeatures && (
                                              <CheckCircle className="h-4 w-4 text-green-600" />
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  ) : (
                                    <div className="text-sm text-muted-foreground p-4 bg-muted rounded-md text-center">
                                      <Users className="h-8 w-8 mx-auto mb-2 opacity-50" />
                                      No users associated with this organization
                                    </div>
                                  )}
                                </div>
                                
                                {config.showAdvancedFeatures && selectedOrg.users && selectedOrg.users.length > 0 && (
                                  <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                                    <div className="text-center">
                                      <p className="text-sm font-medium">Total Users</p>
                                      <p className="text-2xl font-bold text-blue-600">{selectedOrg.users.length}</p>
                                    </div>
                                    <div className="text-center">
                                      <p className="text-sm font-medium">Active Users</p>
                                      <p className="text-2xl font-bold text-green-600">
                                        {selectedOrg.users.filter(u => u.id).length}
                                      </p>
                                    </div>
                                    <div className="text-center">
                                      <p className="text-sm font-medium">Roles</p>
                                      <p className="text-2xl font-bold text-purple-600">
                                        {new Set(selectedOrg.users.map(u => u.role)).size}
                                      </p>
                                    </div>
                                  </div>
                                )}
                              </div>
                              
                              <DialogFooter>
                                <Button variant="outline" onClick={() => setSelectedOrg(null)}>Close</Button>
                                {config.capabilities.includes('manage_users') && (
                                  <Button className="gap-2">
                                    <Users className="h-4 w-4" />
                                    Manage Users
                                  </Button>
                                )}
                                {config.capabilities.includes('edit') && (
                                  <Button className="gap-2">
                                    <Settings className="h-4 w-4" />
                                    Edit Organization
                                  </Button>
                                )}
                              </DialogFooter>
                            </DialogContent>
                          )}
                        </Dialog>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing <strong>{filteredOrganizations.length}</strong> of <strong>{Array.isArray(organizations) ? organizations.length : 0}</strong> organizations
              {filterByType !== 'all' && (
                <span className="ml-1">
                  (filtered by <strong className="capitalize">{filterByType}</strong>)
                </span>
              )}
            </div>
            {config.showAdvancedFeatures && (
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  Active: {metrics.activeOrgs}
                </div>
                <div className="flex items-center gap-1">
                  <AlertCircle className="h-3 w-3 text-amber-600" />
                  Inactive: {metrics.totalOrgs - metrics.activeOrgs}
                </div>
                {voiceEnabled && (
                  <div className="flex items-center gap-1">
                    <Mic className="h-3 w-3 text-blue-600" />
                    Voice Ready
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
