"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { format } from "date-fns";
import { 
  MoreHorizontal, 
  Plus, 
  Search, 
  Building, 
  RefreshCcw, 
  AlertCircle,
  Shield,
  Mic,
  CheckCircle,
  Eye,
  Users,
  Settings,
  <PERSON>rCheck,
  Mail,
  Clock
} from "lucide-react";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useAuth } from "@/contexts/AuthContext";

export default function UsersPage() {
  const { user, isAuthenticated, hasRole, canAccessWithoutPIN } = useAuth();
  const {
    users,
    organizations, 
    isLoading,
    fetchUsers,
    fetchOrganizations,
    error
  } = useDashboardStore();
  const context = useOrganizationContext();
  const apiClient = context?.apiClient;

  const [searchTerm, setSearchTerm] = useState("");
  const [orgFilter, setOrgFilter] = useState("all");
  const [roleFilter, setRoleFilter] = useState("all");
  const [isPinRequired, setIsPinRequired] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(false);
  const [statusFilter, setStatusFilter] = useState("all");
  const [isExporting, setIsExporting] = useState(false);
  const [voiceActive, setVoiceActive] = useState(false);

  // Role-based content configuration
  const getRoleBasedConfig = () => {
    if (hasRole('ADMIN')) {
      return {
        title: "System User Management",
        description: "Comprehensive user administration with advanced permissions and organization management",
        capabilities: ['view_all', 'create', 'edit', 'delete', 'manage_roles', 'bulk_operations', 'export_data', 'voice_commands'],
        showAdvancedFeatures: true,
        canManageAll: true
      };
    } else if (hasRole('BROKER')) {
      return {
        title: "Organization User Management",
        description: "Manage users within your organization and network",
        capabilities: ['view_org', 'invite_users', 'export_contacts'],
        showAdvancedFeatures: false,
        canManageAll: false
      };
    } else {
      return {
        title: "User Directory",
        description: "Browse users in your network",
        capabilities: ['view_network'],
        showAdvancedFeatures: false,
        canManageAll: false
      };
    }
  };

  const config = getRoleBasedConfig();

  // Check user capabilities
  useEffect(() => {
    const checkCapabilities = async () => {
      if (user?.id && apiClient) {
        try {
          // Check PIN requirements for user management
          const response = await apiClient.post('/auth/check-pin-requirement', {
            action: 'MANAGE_USERS',
            context: { user_id: user.id }
          }, false);
          setIsPinRequired(!response.instantAccess);
          
          // Check voice capabilities
          setVoiceEnabled(config.capabilities.includes('voice_commands') && 
                         (user.permissions?.includes('VOICE_OPERATIONS') || false));
        } catch (error) {
          console.error('Error checking capabilities:', error);
          setIsPinRequired(true);
        }
      }
    };

    if (isAuthenticated && user && apiClient) {
      checkCapabilities();
    }
  }, [user, isAuthenticated, apiClient, config.capabilities]);

  const stableFetchUsers = useCallback(() => {
    if (apiClient) {
      fetchUsers(apiClient);
    }
  }, [apiClient, fetchUsers]);

  const stableFetchOrganizations = useCallback(() => {
    if (apiClient) {
      fetchOrganizations(apiClient);
    }
  }, [apiClient, fetchOrganizations]);

  useEffect(() => {
    stableFetchUsers();
    stableFetchOrganizations();
  }, [stableFetchUsers, stableFetchOrganizations]);

  const handleRefresh = () => {
    stableFetchUsers();
    stableFetchOrganizations();
  };

  // Enhanced export functionality with role-based permissions
  const handleExport = async () => {
    if (!config.capabilities.includes('export_data') && !config.capabilities.includes('export_contacts')) {
      alert('Export feature not available for your role');
      return;
    }

    const requiresPIN = !canAccessWithoutPIN('EXPORT_USERS');
    if (requiresPIN && isPinRequired) {
      alert('PIN verification required for export');
      return;
    }

    setIsExporting(true);
    try {
      const exportData = filteredUsers.map(user => ({
        name: user.full_name || user.email.split('@')[0],
        email: user.email,
        role: user.role,
        organization: getOrganizationName(user.organization_id),
        created: format(new Date(user.created_at), 'yyyy-MM-dd'),
        ...(config.showAdvancedFeatures && {
          id: user.id,
          updated: format(new Date(user.updated_at), 'yyyy-MM-dd'),
          organizationId: user.organization_id || 'N/A'
        })
      }));

      // Convert to CSV and download
      const headers = Object.keys(exportData[0] || {});
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => headers.map(header => `"${row[header as keyof typeof row]}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `users-export-${format(new Date(), 'yyyy-MM-dd')}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again.');
    } finally {
      setIsExporting(false);
    }
  };

  // Voice operations integration
  const handleVoiceCommand = () => {
    if (!voiceEnabled) {
      alert('Voice operations not available for your role');
      return;
    }

    setVoiceActive(true);
    setTimeout(() => {
      setVoiceActive(false);
      alert('Voice command processed: "Show admin users"');
      setRoleFilter('admin');
    }, 2000);
  };

  // Enhanced filtering with role-based access and status
  const filteredUsers = Array.isArray(users) 
    ? users.filter(user => {
        if (!user) return false;
        
        // Role-based access control
        if (!config.canManageAll && user.organization_id !== user?.organization_id) {
          return false;
        }
        
        const matchesSearch = searchTerm === "" || 
          (user.full_name && user.full_name.toLowerCase().includes(searchTerm.toLowerCase())) ||
          (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase()));
        
        const matchesOrg = orgFilter === "all" || 
          (orgFilter === "none" && !user.organization_id) ||
          (user.organization_id && String(user.organization_id) === orgFilter);
        
        const matchesRole = roleFilter === "all" || 
          (user.role && user.role.toLowerCase() === roleFilter.toLowerCase());

        const matchesStatus = statusFilter === "all" ||
          (statusFilter === "active" && user.id) ||
          (statusFilter === "inactive" && !user.id);
        
        return matchesSearch && matchesOrg && matchesRole && matchesStatus;
      })
    : [];

  const getOrganizationName = (orgId: number | undefined | null) => {
    if (!orgId) return "No Organization";
    const org = Array.isArray(organizations) ? organizations.find(o => o && o.id === orgId) : undefined;
    return org && org.name ? org.name : "Unknown Organization";
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case "admin":
        return "bg-red-500";
      case "consignor":
        return "bg-green-500";
      case "consignee":
        return "bg-purple-500";
      case "carrier":
        return "bg-blue-500";
      case "broker":
        return "bg-amber-500";
      default:
        return "bg-gray-500";
    }
  };

  const getInitials = (fullName: string | undefined) => {
    if (!fullName) return "";
    return fullName
      .split(" ")
      .map(n => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Enhanced user metrics
  const getUserMetrics = () => {
    const totalUsers = Array.isArray(users) ? users.length : 0;
    const activeUsers = Array.isArray(users) ? users.filter(user => user.id).length : 0;
    
    const roleBreakdown = Array.isArray(users) ? users.reduce((acc, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) : {};

    return { totalUsers, activeUsers, roleBreakdown };
  };

  const metrics = getUserMetrics();

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Users className="h-8 w-8" />
            {config.title}
            {voiceEnabled && (
              <div className="flex items-center gap-1 ml-2">
                <Badge variant="outline" className="text-xs">
                  <Mic className="h-3 w-3 mr-1" />
                  Voice Enabled
                </Badge>
              </div>
            )}
          </h1>
          <p className="text-muted-foreground">{config.description}</p>
        </div>
        <div className="flex items-center gap-2">
          {voiceEnabled && (
            <Button 
              onClick={handleVoiceCommand}
              variant="outline" 
              size="sm" 
              className={`gap-2 ${voiceActive ? 'bg-blue-50 border-blue-300' : ''}`}
              disabled={voiceActive}
            >
              <Mic className={`h-4 w-4 ${voiceActive ? 'animate-pulse text-blue-600' : ''}`} />
              {voiceActive ? 'Listening...' : 'Voice Command'}
            </Button>
          )}
          {(config.capabilities.includes('export_data') || config.capabilities.includes('export_contacts')) && (
            <Button 
              onClick={handleExport}
              variant="outline" 
              size="sm" 
              className="gap-2"
              disabled={isExporting}
            >
              {isExporting ? (
                <RefreshCcw className="h-4 w-4 animate-spin" />
              ) : (
                <CheckCircle className="h-4 w-4" />
              )}
              {isExporting ? 'Exporting...' : 'Export'}
            </Button>
          )}
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            className="gap-1"
            disabled={isLoading}
          >
            <RefreshCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
          {config.capabilities.includes('create') && (
            <Button onClick={() => alert("Create user functionality coming soon!")}>
              <Plus className="mr-2 h-4 w-4" /> New User
            </Button>
          )}
        </div>
      </div>

      {/* Role-based metrics cards */}
      {config.showAdvancedFeatures && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                  <p className="text-2xl font-bold">{metrics.totalUsers}</p>
                </div>
                <Users className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Users</p>
                  <p className="text-2xl font-bold">{metrics.activeUsers}</p>
                </div>
                <UserCheck className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Admins</p>
                  <p className="text-2xl font-bold">{metrics.roleBreakdown.ADMIN || 0}</p>
                </div>
                <Shield className="h-8 w-8 text-muted-foreground" />
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Security Status</p>
                  <p className="text-lg font-bold flex items-center gap-1">
                    <Shield className="h-4 w-4 text-green-600" />
                    {isPinRequired ? 'PIN Required' : 'Verified'}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {error && (
        <div className="bg-destructive/15 border border-destructive/30 text-destructive rounded-md p-4">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            <p><strong>Error:</strong> {error}</p>
          </div>
        </div>
      )}

      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5" />
                User Management
                {isPinRequired && (
                  <Badge variant="outline" className="text-xs">
                    <Shield className="h-3 w-3 mr-1" />
                    PIN Protected
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {config.showAdvancedFeatures ? 
                  'Advanced user management with comprehensive permissions and organization control' :
                  'View and manage users within your network'
                }
              </CardDescription>
            </div>
            {config.showAdvancedFeatures && (
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Settings className="h-4 w-4" />
                Admin Mode
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search users..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={orgFilter} onValueChange={setOrgFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by organization" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Organizations</SelectLabel>
                    <SelectItem value="all">All Organizations</SelectItem>
                    <SelectItem value="none">No Organization</SelectItem>
                    {Array.isArray(organizations) && organizations
                      .filter(org => org && org.id && org.name)
                      .map((org) => (
                        <SelectItem key={org.id} value={String(org.id)}>
                          {org.name}
                        </SelectItem>
                      ))}
                  </SelectGroup>
                </SelectContent>
              </Select>

              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel>Roles</SelectLabel>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="consignor">Consignor</SelectItem>
                    <SelectItem value="consignee">Consignee</SelectItem>
                    <SelectItem value="carrier">Carrier</SelectItem>
                    <SelectItem value="broker">Broker</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>

              {config.showAdvancedFeatures && (
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Status</SelectLabel>
                      <SelectItem value="all">All Users</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          <div className="overflow-auto rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[250px]">User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Organization</TableHead>
                  {config.showAdvancedFeatures && <TableHead>Status</TableHead>}
                  <TableHead>Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(5).fill(0).map((_, idx) => (
                    <TableRow key={idx}>
                      <TableCell colSpan={config.showAdvancedFeatures ? 6 : 5}>
                        <div className="h-6 w-full animate-pulse bg-muted rounded-md"></div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : filteredUsers.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={config.showAdvancedFeatures ? 6 : 5} className="text-center py-6 text-muted-foreground">
                      No users found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-3">
                          <Avatar>
                            <AvatarFallback className="bg-primary/10 text-primary">
                              {user.full_name ? getInitials(user.full_name) : user.email[0].toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <div className="flex items-center gap-2">
                              {user.full_name || user.email.split('@')[0]}
                              {config.showAdvancedFeatures && user.id && (
                                <Badge variant="outline" className="text-xs">
                                  ID: {user.id}
                                </Badge>
                              )}
                            </div>
                            <div className="text-sm text-muted-foreground flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {user.email}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={getRoleBadgeColor(user.role)}>
                          {user.role}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {user.organization_id ? (
                            <>
                              <Building className="h-4 w-4 text-muted-foreground" />
                              <span>{getOrganizationName(user.organization_id)}</span>
                            </>
                          ) : (
                            <span className="text-muted-foreground">No Organization</span>
                          )}
                        </div>
                      </TableCell>
                      {config.showAdvancedFeatures && (
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <span className="text-sm text-green-600">Active</span>
                          </div>
                        </TableCell>
                      )}
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3 text-muted-foreground" />
                          {format(new Date(user.created_at), "MMM dd, yyyy")}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                              <span className="sr-only">Open menu</span>
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56">
                            <DropdownMenuItem onClick={() => alert(`Viewing profile for ${user.full_name || user.email}`)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Profile
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => alert(`Sending email to ${user.email}`)}>
                              <Mail className="mr-2 h-4 w-4" />
                              Send Email
                            </DropdownMenuItem>
                            {config.capabilities.includes('edit') && (
                              <>
                                <DropdownMenuItem onClick={() => alert(`Editing user: ${user.full_name || user.email}`)}>
                                  <Settings className="mr-2 h-4 w-4" />
                                  Edit User
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => alert(`Changing role for: ${user.full_name || user.email}`)}>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Change Role
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => alert(`Assigning organization for: ${user.full_name || user.email}`)}>
                                  <Building className="mr-2 h-4 w-4" />
                                  Assign to Organization
                                </DropdownMenuItem>
                              </>
                            )}
                            {config.capabilities.includes('manage_roles') && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => alert(`Resetting password for: ${user.email}`)}>
                                  <Shield className="mr-2 h-4 w-4" />
                                  Reset Password
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => alert(`Managing permissions for: ${user.full_name || user.email}`)}>
                                  <CheckCircle className="mr-2 h-4 w-4" />
                                  Manage Permissions
                                </DropdownMenuItem>
                              </>
                            )}
                            {config.capabilities.includes('delete') && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem 
                                  className="text-destructive focus:text-destructive"
                                  onClick={() => {
                                    if (confirm(`Are you sure you want to delete ${user.full_name || user.email}? This action cannot be undone.`)) {
                                      alert(`User ${user.full_name || user.email} would be deleted`);
                                    }
                                  }}
                                >
                                  <AlertCircle className="mr-2 h-4 w-4" />
                                  Delete User
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          {/* Enhanced footer with comprehensive metrics */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mt-6 pt-4 border-t space-y-2 sm:space-y-0">
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span>Showing <strong className="text-foreground">{filteredUsers.length}</strong> of <strong className="text-foreground">{Array.isArray(users) ? users.length : 0}</strong> users</span>
              </div>
              {config.showAdvancedFeatures && (
                <>
                  <div className="flex items-center gap-2">
                    <UserCheck className="h-4 w-4" />
                    <span><strong className="text-foreground">{metrics.activeUsers}</strong> active users</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    <span><strong className="text-foreground">{metrics.roleBreakdown.ADMIN || 0}</strong> administrators</span>
                  </div>
                </>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {isPinRequired && (
                <Badge variant="outline" className="text-xs">
                  <Shield className="h-3 w-3 mr-1" />
                  PIN Protected
                </Badge>
              )}
              {voiceEnabled && (
                <Badge variant="outline" className="text-xs">
                  <Mic className="h-3 w-3 mr-1" />
                  Voice Ready
                </Badge>
              )}
              {config.showAdvancedFeatures && (
                <Badge variant="outline" className="text-xs">
                  <Settings className="h-3 w-3 mr-1" />
                  Admin View
                </Badge>
              )}
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <CheckCircle className="h-3 w-3 text-green-600" />
                <span>System Active</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Additional metrics and insights for admin users */}
      {config.showAdvancedFeatures && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Role Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {Object.entries(metrics.roleBreakdown).map(([role, count]) => (
                  <div key={role} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge className={`${getRoleBadgeColor(role)} text-xs`}>
                        {role}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">{count}</span>
                      <div className="w-16 h-2 bg-muted rounded">
                        <div 
                          className={`h-full ${getRoleBadgeColor(role)} rounded`}
                          style={{ width: `${(count / metrics.totalUsers) * 100}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Building className="h-5 w-5" />
                Organization Insights
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Total Organizations</span>
                  <span className="font-medium">{Array.isArray(organizations) ? organizations.length : 0}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Users with Organizations</span>
                  <span className="font-medium">
                    {Array.isArray(users) ? users.filter(u => u.organization_id).length : 0}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Unassigned Users</span>
                  <span className="font-medium">
                    {Array.isArray(users) ? users.filter(u => !u.organization_id).length : 0}
                  </span>
                </div>
                <div className="flex items-center justify-between pt-2 border-t">
                  <span className="text-sm font-medium">Coverage Rate</span>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">
                      {metrics.totalUsers > 0 
                        ? Math.round(((metrics.totalUsers - (Array.isArray(users) ? users.filter(u => !u.organization_id).length : 0)) / metrics.totalUsers) * 100)
                        : 0}%
                    </span>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
