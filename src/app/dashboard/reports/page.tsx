"use client";

import { useState, useEffect, useCallback } from "react";
import { TrendingUp } from "lucide-react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { DashboardChart } from "@/components/dashboard/dashboard-chart";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useAuth } from "@/contexts/AuthContext";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { 
  Download, 
  Package, 
  Clock, 
  AlertTriangle,
  BarChart3,
  FileText,
  RefreshCcw,
  Shield,
  CheckCircle,
  Eye,
  Mic
} from "lucide-react";
import { format } from "date-fns";

export default function ReportsPage() {
  const { user, isAuthenticated, hasRole, hasPermission, canAccessWithoutPIN } = useAuth();
  const context = useOrganizationContext();
  const apiClient = context?.apiClient;
  const { 
    recentShipments: shipments, 
    isLoading, 
    fetchShipments 
  } = useDashboardStore();

  const [dateRange, setDateRange] = useState("30");
  const [reportType, setReportType] = useState("summary");
  const [exportFormat, setExportFormat] = useState("csv");
  const [isPinRequired, setIsPinRequired] = useState(false);
  const [voiceEnabled, setVoiceEnabled] = useState(false);

  // Check user capabilities
  useEffect(() => {
    const checkCapabilities = async () => {
      if (user?.id && apiClient) {
        try {
          // Use the proper apiClient method for checking PIN requirements
          const response = await apiClient.post('/auth/check-pin-requirement', {
            action: 'VIEW_REPORTS',
            context: { user_id: user.id }
          }, false);
          setIsPinRequired(!response.instantAccess);
          
          // Check voice capabilities
          setVoiceEnabled(user.permissions?.includes('VOICE_OPERATIONS') || false);
        } catch (error) {
          console.error('Error checking capabilities:', error);
          // Default to requiring PIN if check fails
          setIsPinRequired(true);
        }
      }
    };

    if (isAuthenticated && user && apiClient) {
      checkCapabilities();
    }
  }, [user, isAuthenticated, apiClient]);

  // Enhanced fetch function with JWT authentication
  const stableFetchShipments = useCallback(async () => {
    if (isAuthenticated && apiClient) {
      try {
        const enhancedShipments = await apiClient.getEnhancedShipments();
        // Note: Enhanced shipments handling will be implemented in store
        console.log('Enhanced shipments loaded:', enhancedShipments.length);
      } catch (error) {
        console.error('Error fetching enhanced shipments:', error);
      }
    }
    // Always fetch using the dashboard store method with apiClient
    if (apiClient) {
      fetchShipments(apiClient);
    }
  }, [isAuthenticated, apiClient, fetchShipments]);

  useEffect(() => {
    stableFetchShipments();
  }, [stableFetchShipments]);

  const handleRefresh = () => {
    stableFetchShipments();
  };

  // Get role-specific page title and description
  const getRoleBasedContent = () => {
    switch (user?.role) {
      case 'ADMIN':
        return {
          title: 'System Reports & Analytics',
          description: 'Comprehensive system-wide analytics and performance reports',
          capabilities: ['Full system access', 'Advanced analytics', 'Export all data', 'User insights']
        };
      case 'BROKER':
        return {
          title: 'Logistics Reports & Analytics',
          description: 'Multi-carrier performance analysis and logistics insights',
          capabilities: ['Multi-carrier analytics', 'Performance reports', 'Route optimization', 'Cost analysis']
        };
      case 'CARRIER':
        return {
          title: 'Carrier Performance Reports',
          description: 'Track your delivery performance and operational metrics',
          capabilities: ['Delivery performance', 'Route analytics', 'Fleet insights', 'Customer feedback']
        };
      case 'CONSIGNOR':
        return {
          title: 'Shipping Reports & Analytics',
          description: 'Monitor your outbound shipments and shipping performance',
          capabilities: ['Outbound tracking', 'Shipping costs', 'Delivery performance', 'Volume analytics']
        };
      case 'CONSIGNEE':
        return {
          title: 'Receiving Reports & Analytics',
          description: 'Track incoming shipments and receiving performance',
          capabilities: ['Inbound tracking', 'Receiving analytics', 'Delivery schedules', 'Performance metrics']
        };
      default:
        return {
          title: 'Reports & Analytics',
          description: 'Analyze performance and export data for your shipments',
          capabilities: ['Basic reporting', 'Standard analytics']
        };
    }
  };

  const roleContent = getRoleBasedContent();

  // Safe array operations
  const safeShipments = Array.isArray(shipments) ? shipments : [];
  
  // Calculate metrics
  const totalShipments = safeShipments.length;
  const deliveredShipments = safeShipments.filter(s => s?.status?.toLowerCase() === 'delivered').length;
  const inTransitShipments = safeShipments.filter(s => s?.status?.toLowerCase() === 'in transit').length;
  const delayedShipments = safeShipments.filter(s => s?.status?.toLowerCase() === 'delayed').length;
  const deliveryRate = totalShipments > 0 ? Math.round((deliveredShipments / totalShipments) * 100) : 0;

  // Get top carriers
  const carrierStats = safeShipments.reduce((acc: Record<string, number>, shipment) => {
    if (shipment?.carrier_id) {
      acc[shipment.carrier_id] = (acc[shipment.carrier_id] || 0) + 1;
    }
    return acc;
  }, {});

  const topCarriers = Object.entries(carrierStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5);

  // Sample chart data based on shipments
  const monthlyShipmentData = [
    { name: "Jan", value: Math.floor(totalShipments * 0.8) },
    { name: "Feb", value: Math.floor(totalShipments * 0.9) },
    { name: "Mar", value: Math.floor(totalShipments * 1.1) },
    { name: "Apr", value: Math.floor(totalShipments * 1.0) },
    { name: "May", value: totalShipments },
    // Add more months as needed or generate dynamically
  ];

  const chartData = {
    labels: monthlyShipmentData.map(d => d.name),
    datasets: [
      {
        label: "Shipments",
        data: monthlyShipmentData.map(d => d.value),
        fill: true,
        borderColor: "hsl(var(--primary))",
        backgroundColor: "hsla(var(--primary), 0.2)",
        tension: 0.3,
        pointBackgroundColor: "hsl(var(--primary))",
        pointBorderColor: "hsl(var(--background))",
        pointHoverBackgroundColor: "hsl(var(--primary))",
        pointHoverBorderColor: "hsl(var(--background))",
      },
    ],
  };

  const handleExport = async () => {
    // Check if user has export permissions
    if (!hasPermission('export_reports')) {
      alert('You do not have permission to export reports. Please contact your administrator.');
      return;
    }

    // Check if PIN is required for this action
    if (isPinRequired && !canAccessWithoutPIN('export_reports')) {
      alert('PIN verification required for exporting reports. Please verify your PIN in the organization settings.');
      return;
    }

    try {
      // Trigger export using the enhanced API client
      if (apiClient) {
        const exportData = {
          report_type: reportType,
          format: exportFormat,
          date_range: dateRange,
          user_role: user?.role,
          organization_id: apiClient.orgId
        };
        
        const result = await apiClient.post('/reports/export', exportData, !canAccessWithoutPIN('export_reports'));
        
        if (voiceEnabled) {
          // Trigger voice notification for export completion
          await apiClient.processVoiceCommand(`Report export completed for ${reportType} as ${exportFormat}`, {
            action: 'export_notification',
            report_type: reportType,
            format: exportFormat
          });
        }
        
        console.log('Export completed:', result);
        alert(`${reportType} report exported successfully as ${exportFormat.toUpperCase()}!`);
      } else {
        // Fallback for demo
        alert(`Exporting ${reportType} report as ${exportFormat.toUpperCase()}...`);
      }
    } catch (error) {
      console.error('Export failed:', error);
      alert('Export failed. Please try again or contact support.');
    }
  };

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">{roleContent.title}</h1>
            {user && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  {user.role}
                </Badge>
                {!isPinRequired && (
                  <Badge variant="outline" className="flex items-center gap-1 text-green-600 border-green-600">
                    <CheckCircle className="h-3 w-3" />
                    PIN-Free Access
                  </Badge>
                )}
                {voiceEnabled && (
                  <Badge variant="outline" className="flex items-center gap-1 text-blue-600 border-blue-600">
                    <Mic className="h-3 w-3" />
                    Voice Enabled
                  </Badge>
                )}
              </div>
            )}
          </div>
          <p className="text-muted-foreground mt-1">{roleContent.description}</p>
          {roleContent.capabilities.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {roleContent.capabilities.map((capability, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {capability}
                </Badge>
              ))}
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            className="gap-1"
            disabled={isLoading}
          >
            <RefreshCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-[140px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleExport}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {hasRole('ADMIN') ? 'System Shipments' : 
               hasRole('BROKER') ? 'Managed Shipments' :
               hasRole('CARRIER') ? 'Assigned Shipments' :
               hasRole('CONSIGNOR') ? 'Your Shipments' :
               hasRole('CONSIGNEE') ? 'Incoming Shipments' : 'Total Shipments'}
            </CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalShipments}</div>
            <p className="text-xs text-muted-foreground">
              +{Math.floor(Math.random() * 20)}% from last month
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {hasRole('CARRIER') ? 'Completion Rate' :
               hasRole('CONSIGNEE') ? 'Receipt Rate' : 'Delivery Rate'}
            </CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{deliveryRate}%</div>
            <p className="text-xs text-muted-foreground">
              {deliveredShipments} of {totalShipments} {hasRole('CARRIER') ? 'completed' : 'delivered'}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {hasRole('CARRIER') ? 'Active Routes' :
               hasRole('CONSIGNEE') ? 'Incoming' : 'In Transit'}
            </CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inTransitShipments}</div>
            <p className="text-xs text-muted-foreground">
              {hasRole('CARRIER') ? 'Routes in progress' :
               hasRole('CONSIGNEE') ? 'Expected deliveries' : 'Currently being shipped'}
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {hasRole('CARRIER') ? 'Issues' :
               hasRole('CONSIGNEE') ? 'Delayed Arrivals' : 'Delays'}
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{delayedShipments}</div>
            <p className="text-xs text-muted-foreground">
              {totalShipments > 0 ? Math.round((delayedShipments / totalShipments) * 100) : 0}% of total shipments
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Shipment Trends Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              {hasRole('ADMIN') ? 'System Performance Trends' :
               hasRole('BROKER') ? 'Brokerage Volume Trends' :
               hasRole('CARRIER') ? 'Route Performance Trends' :
               hasRole('CONSIGNOR') ? 'Shipping Volume Trends' :
               hasRole('CONSIGNEE') ? 'Receiving Volume Trends' : 'Shipment Trends'}
            </CardTitle>
            <CardDescription>
              {hasRole('ADMIN') ? 'System-wide shipment and performance analytics' :
               hasRole('BROKER') ? 'Monthly brokerage volume and efficiency metrics' :
               hasRole('CARRIER') ? 'Route performance and delivery metrics over time' :
               hasRole('CONSIGNOR') ? 'Your shipping patterns and volume trends' :
               hasRole('CONSIGNEE') ? 'Incoming delivery patterns and volumes' : 'Monthly shipment volume over time'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <DashboardChart data={chartData} />
          </CardContent>
        </Card>

        {/* Top Carriers / Role-based insights */}
        <Card>
          <CardHeader>
            <CardTitle>
              {hasRole('ADMIN') ? 'System Top Carriers' :
               hasRole('BROKER') ? 'Partner Carrier Performance' :
               hasRole('CARRIER') ? 'Your Performance Metrics' :
               hasRole('CONSIGNOR') ? 'Your Preferred Carriers' :
               hasRole('CONSIGNEE') ? 'Delivery Partners' : 'Top Carriers'}
            </CardTitle>
            <CardDescription>
              {hasRole('ADMIN') ? 'Most utilized carriers across all organizations' :
               hasRole('BROKER') ? 'Performance ranking of your carrier partners' :
               hasRole('CARRIER') ? 'Your performance compared to industry standards' :
               hasRole('CONSIGNOR') ? 'Carriers most frequently used for your shipments' :
               hasRole('CONSIGNEE') ? 'Carriers delivering to your locations' : 'Most used shipping carriers'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {topCarriers.length > 0 ? (
                topCarriers.map(([carrier, count], index) => (
                  <div key={carrier} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${
                        index === 0 ? 'bg-blue-500' :
                        index === 1 ? 'bg-green-500' :
                        index === 2 ? 'bg-yellow-500' :
                        'bg-gray-400'
                      }`} />
                      <span className="font-medium">{carrier}</span>
                      {hasRole('CARRIER') && index === 0 && (
                        <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                          Your Performance
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {count} {hasRole('CARRIER') ? 'deliveries' : 'shipments'}
                      </Badge>
                      {(hasRole('ADMIN') || hasRole('BROKER')) && voiceEnabled && (
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                          <Eye className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-muted-foreground text-center py-4">
                  {hasRole('CARRIER') ? 'No performance data available' : 'No carrier data available'}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Export Options */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Export Reports
            {!hasPermission('export_reports') && (
              <Badge variant="outline" className="text-amber-600 border-amber-600 ml-2">
                <Shield className="h-3 w-3 mr-1" />
                Limited Access
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            {hasPermission('export_reports') 
              ? 'Generate and download detailed reports' 
              : 'Contact your administrator to enable export permissions'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div>
              <label className="text-sm font-medium mb-2 block">Report Type</label>
              <Select value={reportType} onValueChange={setReportType} disabled={!hasPermission('export_reports')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="summary">Summary Report</SelectItem>
                  <SelectItem value="detailed">Detailed Shipments</SelectItem>
                  <SelectItem value="performance">Performance Analytics</SelectItem>
                  <SelectItem value="carrier">Carrier Analysis</SelectItem>
                  {hasRole('ADMIN') && (
                    <>
                      <SelectItem value="system">System-wide Analytics</SelectItem>
                      <SelectItem value="users">User Activity Report</SelectItem>
                    </>
                  )}
                  {(hasRole('ADMIN') || hasRole('BROKER')) && (
                    <SelectItem value="financial">Financial Analysis</SelectItem>
                  )}
                  {hasRole('CARRIER') && (
                    <SelectItem value="routes">Route Performance</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Format</label>
              <Select value={exportFormat} onValueChange={setExportFormat} disabled={!hasPermission('export_reports')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="csv">CSV</SelectItem>
                  <SelectItem value="xlsx">Excel</SelectItem>
                  <SelectItem value="pdf">PDF</SelectItem>
                  {(hasRole('ADMIN') || hasRole('BROKER')) && (
                    <SelectItem value="json">JSON (API)</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium mb-2 block">Date Range</label>
              <Select value={dateRange} onValueChange={setDateRange} disabled={!hasPermission('export_reports')}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7">Last 7 days</SelectItem>
                  <SelectItem value="30">Last 30 days</SelectItem>
                  <SelectItem value="90">Last 90 days</SelectItem>
                  <SelectItem value="365">Last year</SelectItem>
                  {(hasRole('ADMIN') || hasRole('BROKER')) && (
                    <SelectItem value="all">All Time</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex justify-between items-center mt-4">
            <div className="flex items-center gap-2">
              {voiceEnabled && hasPermission('export_reports') && (
                <Button variant="outline" size="sm" onClick={() => alert('Voice export command coming soon!')}>
                  <Mic className="mr-2 h-4 w-4" />
                  Voice Export
                </Button>
              )}
              {isPinRequired && !canAccessWithoutPIN('export_reports') && (
                <Badge variant="outline" className="text-amber-600 border-amber-600">
                  🔒 PIN Required
                </Badge>
              )}
            </div>
            <Button 
              onClick={handleExport} 
              disabled={!hasPermission('export_reports')}
              className={!hasPermission('export_reports') ? 'opacity-50' : ''}
            >
              <Download className="mr-2 h-4 w-4" />
              {!hasPermission('export_reports') ? 'Export (No Permission)' : 'Generate Report'}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Activity Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {hasRole('ADMIN') ? 'System Activity Overview' :
             hasRole('BROKER') ? 'Brokerage Activity' :
             hasRole('CARRIER') ? 'Your Route Activities' :
             hasRole('CONSIGNOR') ? 'Your Shipment Activity' :
             hasRole('CONSIGNEE') ? 'Incoming Deliveries' : 'Recent Shipment Activity'}
            {voiceEnabled && (
              <Button variant="ghost" size="sm" className="ml-auto">
                <Mic className="h-4 w-4 mr-1" />
                Voice Summary
              </Button>
            )}
          </CardTitle>
          <CardDescription>
            {hasRole('ADMIN') ? 'Latest system-wide updates and status changes' :
             hasRole('BROKER') ? 'Recent updates on managed shipments' :
             hasRole('CARRIER') ? 'Latest updates on your assigned routes' :
             hasRole('CONSIGNOR') ? 'Recent updates on your shipments' :
             hasRole('CONSIGNEE') ? 'Latest status on incoming deliveries' : 'Latest updates and status changes'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="overflow-auto rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    {hasRole('CARRIER') ? 'Route/Tracking #' : 'Tracking #'}
                  </TableHead>
                  <TableHead>Status</TableHead>
                  {(hasRole('ADMIN') || hasRole('BROKER') || hasRole('CONSIGNOR')) && (
                    <TableHead>Carrier</TableHead>
                  )}
                  {hasRole('CONSIGNEE') && (
                    <TableHead>Origin</TableHead>
                  )}
                  {hasRole('CARRIER') && (
                    <TableHead>Next Stop</TableHead>
                  )}
                  <TableHead>
                    {hasRole('CARRIER') ? 'Target Delivery' :
                     hasRole('CONSIGNEE') ? 'Expected Arrival' : 'Last Updated'}
                  </TableHead>
                  {(hasRole('ADMIN') || voiceEnabled) && (
                    <TableHead className="text-right">Actions</TableHead>
                  )}
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(5).fill(0).map((_, idx) => (
                    <TableRow key={idx}>
                      <TableCell colSpan={hasRole('ADMIN') || voiceEnabled ? 6 : 5}>
                        <div className="h-6 w-full animate-pulse bg-muted rounded-md"></div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : safeShipments.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={hasRole('ADMIN') || voiceEnabled ? 6 : 5} className="text-center py-6 text-muted-foreground">
                      {hasRole('CARRIER') ? 'No assigned routes available' :
                       hasRole('CONSIGNEE') ? 'No incoming deliveries' : 'No shipment data available'}
                    </TableCell>
                  </TableRow>
                ) : (
                  safeShipments.slice(0, 10).map((shipment) => (
                    <TableRow key={shipment.id}>
                      <TableCell className="font-medium">
                        {shipment.tracking_number}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{shipment.status}</Badge>
                          {hasRole('CARRIER') && shipment.status === 'in transit' && (
                            <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                              Your Route
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      {(hasRole('ADMIN') || hasRole('BROKER') || hasRole('CONSIGNOR')) && (
                        <TableCell>{shipment.carrier_id || "N/A"}</TableCell>
                      )}
                      {hasRole('CONSIGNEE') && (
                        <TableCell>{shipment.origin_address || "N/A"}</TableCell>
                      )}
                      {hasRole('CARRIER') && (
                        <TableCell>{shipment.destination_address || "N/A"}</TableCell>
                      )}
                      <TableCell>
                        {shipment.expected_delivery_date 
                          ? format(new Date(shipment.expected_delivery_date), "MMM dd, yyyy")
                          : "N/A"}
                      </TableCell>
                      {(hasRole('ADMIN') || voiceEnabled) && (
                        <TableCell className="text-right">
                          <div className="flex items-center gap-1 justify-end">
                            {voiceEnabled && (
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <Mic className="h-3 w-3" />
                              </Button>
                            )}
                            {hasRole('ADMIN') && (
                              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                <Eye className="h-3 w-3" />
                              </Button>
                            )}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
