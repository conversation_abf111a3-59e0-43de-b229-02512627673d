"use client";

import { useState, useEffect, useCallback } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Shipment } from "@/lib/types";
import { format } from "date-fns";
import { Search, MoreHorizontal, Plus, RefreshCcw, Shield, Clock, Mic, AlertCircle } from "lucide-react";
import { ShipmentDetailSheet } from "@/components/dashboard/shipment-detail-sheet";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useAuth } from "@/contexts/AuthContext";

export default function ShipmentsPage() {
  const { 
    recentShipments: shipments, 
    isLoading, 
    fetchShipments 
  } = useDashboardStore();
  const context = useOrganizationContext();
  const apiClient = context?.apiClient;
  const { user, hasRole, hasPermission, canAccessWithoutPIN } = useAuth();

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const stableFetchShipments = useCallback(() => {
    if (apiClient) {
      fetchShipments(apiClient);
    }
  }, [apiClient, fetchShipments]);

  useEffect(() => {
    stableFetchShipments();
  }, [stableFetchShipments]);

  const handleRefresh = () => {
    stableFetchShipments();
  };

  // Make sure shipments is an array before filtering
  const filteredShipments = Array.isArray(shipments) 
    ? shipments.filter(shipment => {
        if (!shipment) return false;
        
        // Apply text search
        const matchesSearch = searchTerm === "" || 
          Object.values(shipment).some(value => 
            value && typeof value === 'string' && value.toLowerCase().includes(searchTerm.toLowerCase())
          );
        
        // Apply status filter safely
        const matchesStatus = statusFilter === "all" || 
          (shipment.status && shipment.status.toLowerCase() === statusFilter.toLowerCase());
    
    return matchesSearch && matchesStatus;
  }) : [];

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return "bg-green-500";
      case "in transit":
        return "bg-blue-500";
      case "delayed":
        return "bg-amber-500";
      case "exception":
        return "bg-red-500";
      case "pending":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };

  // Get role-based page content
  const getRoleBasedContent = () => {
    if (!user) return { 
      title: "Shipments", 
      description: "Manage and track all your shipments",
      capabilities: []
    };
    
    switch (user.role) {
      case 'ADMIN':
        return { 
          title: "All System Shipments", 
          description: "Monitor and manage shipments across all organizations",
          capabilities: ["System-wide visibility", "Full management", "Cross-org operations", "Voice commands"]
        };
      case 'BROKER':
        return { 
          title: "Managed Shipments", 
          description: "Coordinate and track shipments under your management",
          capabilities: ["Create shipments", "Assign carriers", "Track progress", "Voice updates", "Route optimization"]
        };
      case 'CARRIER':
        return { 
          title: "Assigned Shipments", 
          description: "View and update shipments assigned to your routes",
          capabilities: ["Status updates", "Location tracking", "Delivery confirmation", "Voice reports"]
        };
      case 'CONSIGNOR':
        return { 
          title: "Your Shipments", 
          description: "Track shipments you've created and scheduled",
          capabilities: ["Create shipments", "Track progress", "Modify details", "Delivery scheduling"]
        };
      case 'CONSIGNEE':
        return { 
          title: "Incoming Shipments", 
          description: "Monitor deliveries coming to your location",
          capabilities: ["Delivery tracking", "Receipt confirmation", "Schedule coordination"]
        };
      default:
        return { 
          title: "Shipments", 
          description: "Manage and track all your shipments",
          capabilities: []
        };
    }
  };

  const roleContent = getRoleBasedContent();

  return (
    <div className="flex flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold">{roleContent.title}</h1>
            {user && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="flex items-center gap-1">
                  <Shield className="h-3 w-3" />
                  {user.role}
                </Badge>
                {canAccessWithoutPIN('view_shipments') && (
                  <Badge variant="outline" className="flex items-center gap-1 text-green-600 border-green-600">
                    <Clock className="h-3 w-3" />
                    PIN-Free Access
                  </Badge>
                )}
                {hasPermission('voice_operations') && (
                  <Badge variant="outline" className="flex items-center gap-1 text-blue-600 border-blue-600">
                    <Mic className="h-3 w-3" />
                    Voice Enabled
                  </Badge>
                )}
              </div>
            )}
          </div>
          <p className="text-muted-foreground mt-1">{roleContent.description}</p>
          {roleContent.capabilities.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {roleContent.capabilities.map((capability, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {capability}
                </Badge>
              ))}
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            className="gap-1"
            disabled={isLoading}
          >
            <RefreshCcw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Refresh'}
          </Button>
          {hasPermission('create_shipment') && (
            <Button onClick={() => alert("Create new shipment functionality coming soon!")}>
              <Plus className="mr-2 h-4 w-4" /> New Shipment
            </Button>
          )}
        </div>
      </div>

      <Card>
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>
                {hasRole('ADMIN') ? 'System-Wide Shipment Management' :
                 hasRole('BROKER') ? 'Brokerage Shipment Management' :
                 hasRole('CARRIER') ? 'Assignment Management' :
                 hasRole('CONSIGNOR') ? 'Your Shipment Management' :
                 hasRole('CONSIGNEE') ? 'Delivery Management' : 'Shipment Management'}
              </CardTitle>
              <CardDescription>
                {hasRole('ADMIN') ? 'View, filter, and manage all shipments across the entire system' :
                 hasRole('BROKER') ? 'Coordinate and manage shipments under your brokerage' :
                 hasRole('CARRIER') ? 'View and update status for your assigned shipments' :
                 hasRole('CONSIGNOR') ? 'Track and manage shipments you have created' :
                 hasRole('CONSIGNEE') ? 'Monitor incoming deliveries and confirm receipts' :
                 'View, filter, and manage all shipments in the system'}
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              {!hasPermission('view_all_shipments') && !hasPermission('view_org_shipments') && (
                <Badge variant="outline" className="text-amber-600 border-amber-600 text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  Limited Access
                </Badge>
              )}
              {canAccessWithoutPIN('view_shipments') && (
                <Badge variant="outline" className="text-green-600 border-green-600 text-xs">
                  Instant Load
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search shipments..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="in transit">In Transit</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="delayed">Delayed</SelectItem>
                    <SelectItem value="exception">Exception</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Mobile Card Layout */}
          <div className="md:hidden space-y-4">
            {isLoading ? (
              Array(5).fill(0).map((_, idx: number) => (
                <div key={idx} className="bg-white rounded-lg border p-4 shadow-sm">
                  <div className="h-4 w-24 animate-pulse bg-muted rounded-md mb-2"></div>
                  <div className="h-3 w-full animate-pulse bg-muted rounded-md mb-1"></div>
                  <div className="h-3 w-3/4 animate-pulse bg-muted rounded-md"></div>
                </div>
              ))
            ) : filteredShipments.length === 0 ? (
              <div className="text-center py-12 text-muted-foreground bg-white rounded-lg border">
                No shipments found
              </div>
            ) : (
              filteredShipments.map((shipment: Shipment) => (
                <div key={shipment.id} className="bg-white rounded-lg border p-4 shadow-sm space-y-3">
                  {/* Header with tracking number and status */}
                  <div className="flex items-start justify-between">
                    <div>
                      <Button 
                        variant="link" 
                        className="h-auto p-0 text-primary font-semibold text-left"
                        onClick={() => {
                          setSelectedShipment(shipment);
                          setIsSheetOpen(true);
                        }}
                      >
                        {shipment.tracking_number}
                      </Button>
                      {shipment.external_reference_number && (
                        <div className="text-xs text-muted-foreground mt-1">
                          {shipment.external_reference_number}
                        </div>
                      )}
                    </div>
                    <div className="flex flex-col items-end gap-1">
                      <Badge className={getStatusColor(shipment.status)}>
                        {shipment.status}
                      </Badge>
                      {hasRole('CARRIER') && shipment.status === 'in transit' && (
                        <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                          Your Route
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Route Information */}
                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="text-muted-foreground font-medium">From: </span>
                      <span className="text-gray-900">{shipment.origin_address || "N/A"}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground font-medium">To: </span>
                      <span className="text-gray-900">{shipment.destination_address || "N/A"}</span>
                    </div>
                    {(hasRole('ADMIN') || hasRole('BROKER') || hasRole('CONSIGNOR')) && shipment.carrier && (
                      <div>
                        <span className="text-muted-foreground font-medium">Carrier: </span>
                        <span className="text-gray-900">{shipment.carrier?.full_name || shipment.carrier?.email || 'N/A'}</span>
                      </div>
                    )}
                    <div>
                      <span className="text-muted-foreground font-medium">
                        {hasRole('CARRIER') ? 'Target Delivery: ' : 
                         hasRole('CONSIGNEE') ? 'Expected Arrival: ' : 'Est. Delivery: '}
                      </span>
                      <span className="text-gray-900">
                        {shipment.expected_delivery_date 
                          ? format(new Date(shipment.expected_delivery_date), 'MMM dd, yyyy')
                          : 'TBD'
                        }
                      </span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex gap-2 pt-2 border-t">
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="flex-1 text-xs"
                      onClick={() => {
                        setSelectedShipment(shipment);
                        setIsSheetOpen(true);
                      }}
                    >
                      View Details
                    </Button>
                    {canAccessWithoutPIN('edit_shipment') && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="text-xs"
                        onClick={() => {
                          // Quick action logic here
                          console.log('Quick action for shipment:', shipment.id);
                        }}
                      >
                        Quick Update
                      </Button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Desktop Table Layout */}
          <div className="hidden md:block overflow-auto rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID/Tracking #</TableHead>
                  <TableHead>Origin</TableHead>
                  <TableHead>Destination</TableHead>
                  <TableHead>Status</TableHead>
                  {(hasRole('ADMIN') || hasRole('BROKER') || hasRole('CONSIGNOR')) && (
                    <TableHead>Carrier</TableHead>
                  )}
                  <TableHead>
                    {hasRole('CARRIER') ? 'Target Delivery' : 
                     hasRole('CONSIGNEE') ? 'Expected Arrival' : 'Est. Delivery'}
                  </TableHead>
                  <TableHead className="text-right">
                    {canAccessWithoutPIN('edit_shipment') ? 'Quick Actions' : 'Actions'}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(5).fill(0).map((_, idx: number) => (
                  <TableRow key={idx}>
                    <TableCell colSpan={7}>
                    <div className="h-6 w-full animate-pulse bg-muted rounded-md"></div>
                    </TableCell>
                  </TableRow>
                  ))
                ) : filteredShipments.length === 0 ? (
                  <TableRow>
                  <TableCell colSpan={7} className="text-center py-6 text-muted-foreground">
                    No shipments found
                  </TableCell>
                  </TableRow>
                ) : (
                  filteredShipments.map((shipment: Shipment) => (
                  <TableRow key={shipment.id}>
                    <TableCell className="font-medium">
                    <div>
                      <Button 
                      variant="link" 
                      className="h-auto p-0 text-primary font-medium"
                      onClick={() => {
                        setSelectedShipment(shipment);
                        setIsSheetOpen(true);
                      }}
                      >
                      {shipment.tracking_number}
                      </Button>
                      <div className="text-xs text-muted-foreground">
                      {shipment.external_reference_number}
                      </div>
                    </div>
                    </TableCell>
                    <TableCell>{shipment.origin_address || "N/A"}</TableCell>
                    <TableCell>{shipment.destination_address || "N/A"}</TableCell>
                    <TableCell>
                    <div className="flex items-center gap-2">
                      <Badge className={getStatusColor(shipment.status)}>
                        {shipment.status}
                      </Badge>
                      {hasRole('CARRIER') && shipment.status === 'in transit' && (
                        <Badge variant="outline" className="text-blue-600 border-blue-600 text-xs">
                          Your Route
                        </Badge>
                      )}
                    </div>
                    </TableCell>
                    {(hasRole('ADMIN') || hasRole('BROKER') || hasRole('CONSIGNOR')) && (
                      <TableCell>{shipment.carrier_id || "N/A"}</TableCell>
                    )}
                    <TableCell>
                    {shipment.expected_delivery_date 
                      ? format(new Date(shipment.expected_delivery_date), "MMM dd, yyyy")
                      : "N/A"}
                    </TableCell>
                    <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                        <span className="sr-only">Open menu</span>
                      </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => {
                        setSelectedShipment(shipment);
                        setIsSheetOpen(true);
                      }}>
                        View Details
                      </DropdownMenuItem>
                      {hasPermission('update_shipment_status') && (
                        <DropdownMenuItem>
                          {hasRole('CARRIER') ? 'Update Status' : 'Add Update'}
                        </DropdownMenuItem>
                      )}
                      {hasPermission('voice_operations') && (
                        <>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-blue-600">
                            <Mic className="h-4 w-4 mr-2" />
                            Voice Update
                          </DropdownMenuItem>
                        </>
                      )}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Share</DropdownMenuItem>
                      {hasPermission('delete_shipment') && (
                        <DropdownMenuItem className="text-destructive">
                          {!canAccessWithoutPIN('delete_shipment') && '🔒 '}
                          Cancel Shipment
                        </DropdownMenuItem>
                      )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                    </TableCell>
                  </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
          
          <div className="flex items-center justify-end mt-4">
            <div className="text-sm text-muted-foreground">
              Showing <strong>{filteredShipments.length}</strong> of <strong>{Array.isArray(shipments) ? shipments.length : 0}</strong> shipments
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Sheet component for showing shipment details */}
      <ShipmentDetailSheet 
        shipment={selectedShipment} 
        isOpen={isSheetOpen} 
        onOpenChange={setIsSheetOpen} 
      />
    </div>
  );
}
