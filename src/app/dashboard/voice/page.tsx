"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Mic, 
  Settings, 
  Phone, 
  MessageSquare, 
  Webhook, 
  Bot, 
  Volume2,
  Eye,
  EyeOff,
  Copy,
  Check,
  Plus,
  Edit,
  Trash2
} from "lucide-react";
import { VAPIAssistant, VAPICall, VAPISettings } from "@/lib/types";
import { useAuth } from "@/contexts/AuthContext";

export default function VoicePage() {
  const { canAccessWithoutPIN } = useAuth();
  const [assistants, setAssistants] = useState<VAPIAssistant[]>([]);
  const [recentCalls, setRecentCalls] = useState<VAPICall[]>([]);
  const [settings, setSettings] = useState<VAPISettings>({
    api_key: "",
    webhook_secret: "",
    voice_commands_enabled: true,
    auto_transcription: true,
    webhook_configs: []
  });
  const [showApiKey, setShowApiKey] = useState(false);
  const [showWebhookSecret, setShowWebhookSecret] = useState(false);
  const [copiedField, setCopiedField] = useState<string | null>(null);
  const [isCreatingAssistant, setIsCreatingAssistant] = useState(false);

  // Mock data for demo - in real app, this would come from API
  useEffect(() => {
    // Simulate loading assistants
    setAssistants([
      {
        id: "asst_1",
        name: "PACE Freight Assistant",
        description: "Main freight tracking and logistics assistant",
        model: {
          provider: "openai",
          model: "gpt-4",
          temperature: 0.7,
          functions: [
            {
              name: "get_shipment_status",
              description: "Get shipment tracking information",
              parameters: {
                type: "object",
                properties: {
                  tracking_number: { type: "string" }
                },
                required: ["tracking_number"]
              }
            }
          ]
        },
        voice: {
          provider: "openai",
          voiceId: "alloy"
        },
        serverUrl: process.env.NODE_ENV === 'development' 
          ? "http://localhost:4030/api/vapi-tools" 
          : `${process.env.NEXT_PUBLIC_API_URL}/api/vapi-tools`,
        created_at: "2024-01-15T10:00:00Z",
        updated_at: "2024-01-20T14:30:00Z",
        is_active: true
      }
    ]);

    // Simulate loading recent calls
    setRecentCalls([
      {
        id: "call_1",
        assistant_id: "asst_1",
        phone_number: "+**********",
        status: "ended",
        started_at: "2024-01-20T14:00:00Z",
        ended_at: "2024-01-20T14:03:45Z",
        cost: 0.12,
        transcript: "User asked about shipment PACE123456. Provided status update.",
        created_at: "2024-01-20T14:00:00Z"
      }
    ]);
  }, []);

  const copyToClipboard = async (text: string, field: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(field);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  const handleCreateAssistant = () => {
    setIsCreatingAssistant(true);
    
    // Auto-create assistant with smart defaults
    const newAssistant: VAPIAssistant = {
      id: `asst_${Date.now()}`,
      name: `PACE Assistant ${assistants.length + 1}`,
      description: "AI assistant for freight and logistics operations",
      model: {
        provider: "openai",
        model: "gpt-4",
        temperature: 0.7,
        functions: [
          {
            name: "get_shipment_status",
            description: "Get real-time shipment tracking information and status updates",
            parameters: {
              type: "object",
              properties: {
                tracking_number: { type: "string", description: "The tracking number of the shipment" }
              },
              required: ["tracking_number"]
            }
          },
          {
            name: "create_shipment_alert",
            description: "Create alerts for shipment delays or issues",
            parameters: {
              type: "object",
              properties: {
                tracking_number: { type: "string", description: "The tracking number of the shipment" },
                alert_type: { type: "string", enum: ["delay", "damage", "customs_hold", "delivery_attempt"] },
                message: { type: "string", description: "Custom alert message" }
              },
              required: ["tracking_number", "alert_type"]
            }
          },
          {
            name: "notify_stakeholders",
            description: "Send notifications to relevant stakeholders about shipment updates",
            parameters: {
              type: "object",
              properties: {
                tracking_number: { type: "string", description: "The tracking number of the shipment" },
                stakeholder_types: { type: "array", items: { type: "string" } },
                message: { type: "string", description: "Notification message" }
              },
              required: ["tracking_number", "stakeholder_types", "message"]
            }
          }
        ]
      },
      voice: {
        provider: "openai",
        voiceId: "alloy"
      },
      serverUrl: process.env.NODE_ENV === 'development' 
        ? "http://localhost:4030/api/vapi-tools" 
        : `${process.env.NEXT_PUBLIC_API_URL}/api/vapi-tools`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true
    };

    // Add to assistants list (in real app, this would be an API call)
    setAssistants([...assistants, newAssistant]);
    setIsCreatingAssistant(false);
  };

  return (
    <div className="flex flex-col gap-6 p-6 bg-gradient-to-br from-slate-50 via-white to-blue-50 min-h-full">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-manrope font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent">
            Voice Intelligence
          </h1>
          <p className="text-gray-600 font-inter mt-1">
            Configure VAPI assistants, manage voice calls, and set up webhooks
          </p>
        </div>
        <div className="flex items-center gap-2">
          {canAccessWithoutPIN('manage_voice_settings') ? (
            <Badge variant="outline" className="bg-gradient-to-r from-green-50 to-teal-50 border-green-200">
              <span className="bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent font-medium">Full Access</span>
            </Badge>
          ) : (
            <Badge variant="outline" className="bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200">
              <span className="bg-gradient-to-r from-amber-600 to-amber-700 bg-clip-text text-transparent font-medium">PIN Required</span>
            </Badge>
          )}
        </div>
      </div>

      <Tabs defaultValue="assistants" className="w-full">
        <TabsList className="bg-white/60 backdrop-blur-sm border border-blue-100 shadow-md">
          <TabsTrigger value="assistants" className="font-inter data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-50 data-[state=active]:to-purple-50 data-[state=active]:text-blue-700">
            <Bot className="h-4 w-4 mr-2" />
            Assistants
          </TabsTrigger>
          <TabsTrigger value="calls" className="font-inter data-[state=active]:bg-gradient-to-r data-[state=active]:from-teal-50 data-[state=active]:to-blue-50 data-[state=active]:text-teal-700">
            <Phone className="h-4 w-4 mr-2" />
            Call History
          </TabsTrigger>
          <TabsTrigger value="webhooks" className="font-inter data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-50 data-[state=active]:to-blue-50 data-[state=active]:text-purple-700">
            <Webhook className="h-4 w-4 mr-2" />
            Webhooks
          </TabsTrigger>
          <TabsTrigger value="settings" className="font-inter data-[state=active]:bg-gradient-to-r data-[state=active]:from-amber-50 data-[state=active]:to-red-50 data-[state=active]:text-amber-700">
            <Settings className="h-4 w-4 mr-2" />
            Settings
          </TabsTrigger>
        </TabsList>

        <TabsContent value="assistants">
          <div className="grid gap-4">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-xl font-manrope font-semibold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Voice Assistants
                </h2>
                <p className="text-sm font-inter text-gray-600 mt-1">
                  Functions are automatically configured based on your role and permissions
                </p>
              </div>
              <Button 
                onClick={handleCreateAssistant}
                disabled={isCreatingAssistant}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-inter"
              >
                <Plus className="h-4 w-4 mr-2" />
                {isCreatingAssistant ? "Creating..." : "Quick Setup"}
              </Button>
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {assistants.map((assistant) => (
                <Card key={assistant.id} className="bg-white/70 backdrop-blur-sm border-blue-100 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="font-manrope text-lg bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                        {assistant.name}
                      </CardTitle>
                      <Badge 
                        variant={assistant.is_active ? "default" : "secondary"}
                        className={assistant.is_active 
                          ? "bg-gradient-to-r from-green-500 to-teal-500 text-white" 
                          : "bg-gray-100 text-gray-600"
                        }
                      >
                        {assistant.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <CardDescription className="font-inter text-gray-600">
                      {assistant.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div>
                        <Label className="text-xs font-inter font-medium text-gray-500">Model</Label>
                        <p className="text-sm font-inter">{assistant.model.provider} / {assistant.model.model}</p>
                      </div>
                      <div>
                        <Label className="text-xs font-inter font-medium text-gray-500">Voice</Label>
                        <p className="text-sm font-inter">{assistant.voice.provider} / {assistant.voice.voiceId}</p>
                      </div>
                      <div>
                        <Label className="text-xs font-inter font-medium text-gray-500">Functions</Label>
                        <p className="text-sm font-inter">{assistant.model.functions?.length || 0} available</p>
                      </div>
                      <div className="flex gap-2 pt-2">
                        <Button variant="outline" size="sm" className="font-inter">
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                        <Button variant="outline" size="sm" className="font-inter text-red-600 hover:bg-red-50">
                          <Trash2 className="h-3 w-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="calls">
          <Card className="bg-white/70 backdrop-blur-sm border-teal-100 shadow-lg">
            <CardHeader>
              <CardTitle className="font-manrope bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent">
                Recent Voice Calls
              </CardTitle>
              <CardDescription className="font-inter text-gray-600">
                Monitor and review voice interactions with your assistants
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentCalls.map((call) => (
                  <div key={call.id} className="flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-teal-50 rounded-lg border border-blue-100">
                    <div className="flex items-center gap-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full flex items-center justify-center">
                        <Phone className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <p className="font-inter font-medium">{call.phone_number}</p>
                        <p className="text-sm font-inter text-gray-600">
                          {new Date(call.started_at!).toLocaleDateString()} • Duration: {call.ended_at ? '3m 45s' : 'In progress'}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Badge 
                        variant={call.status === 'ended' ? 'default' : 'secondary'}
                        className={call.status === 'ended' 
                          ? "bg-gradient-to-r from-green-500 to-teal-500 text-white" 
                          : "bg-gradient-to-r from-blue-500 to-purple-500 text-white"
                        }
                      >
                        {call.status}
                      </Badge>
                      {call.cost && (
                        <span className="text-sm font-inter text-gray-600">${call.cost.toFixed(2)}</span>
                      )}
                      <Button variant="outline" size="sm" className="font-inter">
                        <MessageSquare className="h-3 w-3 mr-1" />
                        Transcript
                      </Button>
                    </div>
                  </div>
                ))}
                {recentCalls.length === 0 && (
                  <div className="text-center py-8 text-gray-500 font-inter">
                    No voice calls yet. Create an assistant and start making calls!
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks">
          <Card className="bg-white/70 backdrop-blur-sm border-purple-100 shadow-lg">
            <CardHeader>
              <CardTitle className="font-manrope bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
                Webhook Configuration
              </CardTitle>
              <CardDescription className="font-inter text-gray-600">
                Configure webhook endpoints for VAPI events and real-time notifications
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label htmlFor="webhook-url" className="font-inter font-medium">Webhook URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="webhook-url"
                        value={process.env.NODE_ENV === 'development' 
                          ? "http://localhost:3001/api/webhooks/vapi/events" 
                          : "https://your-domain.com/api/webhooks/vapi/events"
                        }
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(
                          process.env.NODE_ENV === 'development' 
                            ? "http://localhost:3001/api/webhooks/vapi/events" 
                            : "https://your-domain.com/api/webhooks/vapi/events",
                          "webhook-url"
                        )}
                      >
                        {copiedField === "webhook-url" ? 
                          <Check className="h-4 w-4 text-green-600" /> : 
                          <Copy className="h-4 w-4" />
                        }
                      </Button>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="server-url" className="font-inter font-medium">Server Tools URL</Label>
                    <div className="flex gap-2">
                      <Input
                        id="server-url"
                        value={process.env.NODE_ENV === 'development' 
                          ? "http://localhost:3001/api/vapi-tools" 
                          : "https://your-domain.com/api/vapi-tools"
                        }
                        readOnly
                        className="font-mono text-sm"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => copyToClipboard(
                          process.env.NODE_ENV === 'development' 
                            ? "http://localhost:3001/api/vapi-tools" 
                            : "https://your-domain.com/api/vapi-tools",
                          "server-url"
                        )}
                      >
                        {copiedField === "server-url" ? 
                          <Check className="h-4 w-4 text-green-600" /> : 
                          <Copy className="h-4 w-4" />
                        }
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
                  <h4 className="font-manrope font-semibold mb-2 text-blue-700">Setup Instructions</h4>
                  <ol className="font-inter text-sm text-blue-600 space-y-1">
                    <li>1. Copy the webhook URL above</li>
                    <li>2. Add it to your VAPI dashboard under Webhooks</li>
                    <li>3. Configure your assistant to use the Server Tools URL</li>
                    <li>4. Test the connection with a sample call</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings">
          <div className="grid gap-6">
            <Card className="bg-white/70 backdrop-blur-sm border-amber-100 shadow-lg">
              <CardHeader>
                <CardTitle className="font-manrope bg-gradient-to-r from-amber-600 to-red-600 bg-clip-text text-transparent">
                  VAPI API Configuration
                </CardTitle>
                <CardDescription className="font-inter text-gray-600">
                  Configure your VAPI API credentials and global settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="api-key" className="font-inter font-medium">VAPI API Key</Label>
                    <div className="flex gap-2">
                      <Input
                        id="api-key"
                        type={showApiKey ? "text" : "password"}
                        value={settings.api_key || "Not configured"}
                        onChange={(e) => setSettings({...settings, api_key: e.target.value})}
                        placeholder="Enter your VAPI API key"
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setShowApiKey(!showApiKey)}
                      >
                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="webhook-secret" className="font-inter font-medium">Webhook Secret</Label>
                    <div className="flex gap-2">
                      <Input
                        id="webhook-secret"
                        type={showWebhookSecret ? "text" : "password"}
                        value={settings.webhook_secret || "Auto-generated"}
                        onChange={(e) => setSettings({...settings, webhook_secret: e.target.value})}
                        placeholder="Auto-generated webhook secret"
                        className="font-mono"
                      />
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => setShowWebhookSecret(!showWebhookSecret)}
                      >
                        {showWebhookSecret ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="default-assistant" className="font-inter font-medium">Default Assistant</Label>
                    <Select value={settings.default_assistant_id} onValueChange={(value) => setSettings({...settings, default_assistant_id: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select default assistant" />
                      </SelectTrigger>
                      <SelectContent>
                        {assistants.map((assistant) => (
                          <SelectItem key={assistant.id} value={assistant.id}>
                            {assistant.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex justify-end">
                    <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-inter">
                      Save Settings
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white/70 backdrop-blur-sm border-green-100 shadow-lg">
              <CardHeader>
                <CardTitle className="font-manrope bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent">
                  Voice Features
                </CardTitle>
                <CardDescription className="font-inter text-gray-600">
                  Enable and configure voice-powered features for your freight operations
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-inter font-medium">Voice Commands</p>
                      <p className="text-sm font-inter text-gray-600">Enable voice-activated shipment queries</p>
                    </div>
                    <Button
                      variant={settings.voice_commands_enabled ? "default" : "outline"}
                      onClick={() => setSettings({...settings, voice_commands_enabled: !settings.voice_commands_enabled})}
                      className={settings.voice_commands_enabled 
                        ? "bg-gradient-to-r from-green-500 to-teal-500 text-white" 
                        : ""
                      }
                    >
                      {settings.voice_commands_enabled ? <Volume2 className="h-4 w-4 mr-2" /> : <Mic className="h-4 w-4 mr-2" />}
                      {settings.voice_commands_enabled ? "Enabled" : "Disabled"}
                    </Button>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-inter font-medium">Auto Transcription</p>
                      <p className="text-sm font-inter text-gray-600">Automatically transcribe all voice interactions</p>
                    </div>
                    <Button
                      variant={settings.auto_transcription ? "default" : "outline"}
                      onClick={() => setSettings({...settings, auto_transcription: !settings.auto_transcription})}
                      className={settings.auto_transcription 
                        ? "bg-gradient-to-r from-blue-500 to-purple-500 text-white" 
                        : ""
                      }
                    >
                      {settings.auto_transcription ? <MessageSquare className="h-4 w-4 mr-2" /> : <MessageSquare className="h-4 w-4 mr-2" />}
                      {settings.auto_transcription ? "Enabled" : "Disabled"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}