"use client";

import { useEffect, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { LoginForm } from "@/components/auth/login-form";
import { NavigationHeader } from "@/components/layout/navigation-header";
import Link from "next/link";
import { ArrowLeft } from "lucide-react";

function LoginPageContent() {
  const { isAuthenticated } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Redirect if already authenticated (middleware will handle this too, but good to have as backup)
  useEffect(() => {
    if (isAuthenticated) {
      const redirectTo = searchParams.get('redirect') || '/dashboard';
      router.replace(redirectTo);
    }
  }, [isAuthenticated, router, searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      <NavigationHeader />
      
      <div className="flex flex-col items-center justify-center px-4 py-8 sm:py-16">
        {/* Back to home link */}
        <div className="w-full max-w-md mb-6">
          <Link 
            href="/"
            className="inline-flex items-center text-sm text-gray-600 hover:text-blue-600 transition-colors"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>
        </div>

        {/* Login Form */}
        <div className="w-full max-w-md">
          <LoginForm />
        </div>

        {/* Additional links */}
        <div className="w-full max-w-md mt-8 text-center">
          <div className="space-y-4">
            <div className="text-sm text-gray-500">
              New to PACE?{" "}
              <Link href="/contact" className="text-blue-600 hover:text-blue-700 font-medium">
                Contact us for access
              </Link>
            </div>
            <div className="text-xs text-gray-400">
              Need help?{" "}
              <Link href="/support" className="text-blue-600 hover:text-blue-700">
                Visit our support center
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <LoginPageContent />
    </Suspense>
  );
}
