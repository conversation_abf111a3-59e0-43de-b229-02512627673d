"use client";

import React, { useState, useEffect } from 'react';
import { Organization } from '@/lib/types';
import { useOrganizationContext } from '@/contexts/OrganizationContext';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Building, Lock, ArrowRight, Loader2, AlertCircle } from 'lucide-react';

export function OrganizationSelector() {
  const context = useOrganizationContext();
  const { user } = useAuth();
  const [selectedOrgId, setSelectedOrgId] = useState<string | null>(null);
  const [pinInput, setPinInput] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [autoSelectFirstOrg, setAutoSelectFirstOrg] = useState<boolean>(true);

  // Extract context values with fallback to avoid conditional hook calls
  const { 
    fetchAllOrganizations, 
    organizations = [], 
    isLoadingOrganizations = false, 
    setOrganizationContext,
    organizationId
  } = context || {};

  // Effect to fetch all organizations when component mounts
  useEffect(() => {
    if (!context) return; // Early return if no context
    
    const loadOrganizations = async () => {
      if (organizations.length === 0 && !isLoadingOrganizations) {
        try {
          console.log("Fetching organizations from API...");
          await fetchAllOrganizations();
          // Will be handled by second useEffect
        } catch (err: unknown) {
          console.error("Failed to fetch organizations:", err);
          setError((err as Error)?.message || "Failed to fetch organizations. Please try again.");
        }
      }
    };
    
    loadOrganizations();
  }, [context, fetchAllOrganizations, isLoadingOrganizations, organizations.length]);
  
  // Separate effect for auto-selection to avoid race conditions
  useEffect(() => {
    if (!context) return; // Early return if no context
    
    // Reset error when organizations are successfully loaded
    if (organizations.length > 0 && error) {
      setError(null);
    }
    
    // Auto-select the first organization in the list if available
    if (autoSelectFirstOrg && organizations.length > 0) {
      const firstOrg = organizations[0];
      console.log("Auto-selecting first organization:", firstOrg.name || firstOrg.id);
      setSelectedOrgId(firstOrg.id.toString()); // Ensure string conversion
      
      // Also update the selected organization in context
      if (context.setSelectedOrganization) {
        context.setSelectedOrganization(firstOrg);
      }
      
      setAutoSelectFirstOrg(false);
    }
  }, [context, organizations, autoSelectFirstOrg, error]);

  // Conditional return moved after all hooks
  if (!context) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-pulse flex items-center gap-2">
          <Loader2 className="h-5 w-5 animate-spin" />
          <p>Loading organization context...</p>
        </div>
      </div>
    );
  }

  const handleSubmitContext = async () => {
    try {
      if (!selectedOrgId) {
        setError("Please select an organization.");
        return;
      }
      if (!pinInput) {
        setError("Please enter the PIN for the selected organization.");
        return;
      }
      
      setError(null);
      
      // In production we would verify the PIN with an API call here:
      // const isValidPin = await context.apiClient.post(
      //   `/organization/${selectedOrgId}/verify-pin`, 
      //   { pin: pinInput }
      // );
      
      // For now, we'll just set the context directly as per documentation:
      // "Only organization ID header is needed for basic info"
      setOrganizationContext(selectedOrgId);
      
      console.log(`Organization context set to: ID=${selectedOrgId}`);
    } catch (err: unknown) {
      setError((err as Error)?.message || "Invalid organization PIN. Please try again.");
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto shadow-lg">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold">Organization Access</CardTitle>
        <CardDescription>
          {user?.full_name ? `Welcome ${user.full_name}! ` : ''}
          Select your organization and enter your PIN to access the dashboard
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Loading state for organizations */}
        {isLoadingOrganizations && (
          <div className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2">
              <Loader2 className="h-5 w-5 animate-spin" />
              <p>Loading your organizations...</p>
            </div>
          </div>
        )}

        {/* No organizations found */}
        {!isLoadingOrganizations && organizations.length === 0 && (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">No Organizations Found</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You don&apos;t appear to be associated with any organizations.
            </p>
            <p className="text-xs text-muted-foreground">
              Please contact your administrator to get access to an organization.
            </p>
          </div>
        )}

        {/* Organization Selection - Only show if organizations are available */}
        {!isLoadingOrganizations && organizations.length > 0 && (
          <>
            <div className="space-y-2">
              <label htmlFor="organizationSelect" className="text-sm font-medium">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 opacity-70" />
                  <span>Select Organization</span>
                </div>
              </label>
              <Select 
                value={selectedOrgId || ""} 
                onValueChange={(value) => {
                  setSelectedOrgId(value);
                  // Find the selected org and update context
                  const selectedOrg = organizations.find((org: Organization) => org.id.toString() === value.toString());
                  if (selectedOrg && context.setSelectedOrganization) {
                    context.setSelectedOrganization(selectedOrg);
                  }
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="-- Select an Organization --" />
                </SelectTrigger>
                <SelectContent>
                  {organizations.map((org: Organization) => (
                    <SelectItem key={org.id} value={org.id.toString()}>
                      {org.name} {org.id && <span className="text-xs text-muted-foreground ml-2">(ID: {org.id})</span>}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* PIN Input - Only show if organization is selected */}
            {selectedOrgId && (
              <div className="space-y-2">
                <label htmlFor="orgPin" className="text-sm font-medium">
                  <div className="flex items-center gap-2">
                    <Lock className="h-4 w-4 opacity-70" />
                    <span>Organization PIN</span>
                  </div>
                </label>
                <Input 
                  id="orgPin"
                  type="password" 
                  value={pinInput} 
                  onChange={(e) => setPinInput(e.target.value)} 
                  placeholder="Enter Organization PIN"
                />
              </div>
            )}
            
            {/* Submit Button */}
            {selectedOrgId && pinInput && (
              <Button 
                onClick={handleSubmitContext} 
                className="w-full mt-2"
              >
                Continue to Dashboard <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </>
        )}
        
        {/* Error Display */}
        {error && (
          <div className="bg-destructive/10 text-destructive text-sm p-3 rounded-md">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4" />
              {error}
            </div>
          </div>
        )}

        {/* Current Organization Display */}
        {organizationId && (
          <div className="mt-4 pt-4 border-t border-border">
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="bg-primary/10">
                Current Organization: {organizationId}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}