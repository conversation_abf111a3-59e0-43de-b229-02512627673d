"use client";

import { useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { initializeAnalytics, identifyUser } from "@/lib/analytics";

export default function AnalyticsProvider({
  children
}: {
  children: React.ReactNode;
}) {
  const { user } = useAuth();
  
  useEffect(() => {
    // Initialize LogRocket when component mounts (only runs client-side)
    initializeAnalytics();
  }, []);
  
  useEffect(() => {
    // Identify user whenever user data changes
    if (user) {
      identifyUser(user);
    }
  }, [user]);

  return <>{children}</>;
};
