"use client";

import { TrendingUp } from "lucide-react";
import { Area, AreaChart, CartesianGrid, XAxis, YAxis } from "recharts";
import {
  ChartConfig,
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";

interface DashboardChartProps {
  data: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
      fill?: boolean;
    }>;
  };
}

export function DashboardChart({ data }: DashboardChartProps) {
  // Transform Chart.js data format to Recharts format
  const chartData = data.labels.map((label, index) => ({
    month: label,
    shipments: data.datasets[0]?.data[index] || 0,
  }));

  const chartConfig = {
    shipments: {
      label: "Shipments",
      color: "hsl(var(--chart-1))",
    },
  } satisfies ChartConfig;

  // Calculate trend (comparing last two data points)
  const lastTwoValues = chartData.slice(-2);
  const hasIncrease = lastTwoValues.length === 2 && 
    lastTwoValues[1].shipments > lastTwoValues[0].shipments;
  const trendPercentage = lastTwoValues.length === 2 && lastTwoValues[0].shipments > 0 
    ? Math.abs(((lastTwoValues[1].shipments - lastTwoValues[0].shipments) / lastTwoValues[0].shipments) * 100).toFixed(1)
    : "0.0";

  // Get max value for better Y-axis scaling
  const maxValue = Math.max(...chartData.map(d => d.shipments));
  const yAxisMax = maxValue > 0 ? Math.ceil(maxValue * 1.1) : 10;

  return (
    <div className="w-full">
      <ChartContainer config={chartConfig}>
        <AreaChart
          accessibilityLayer
          data={chartData}
          margin={{
            left: 12,
            right: 12,
            top: 12,
            bottom: 12,
          }}
        >
          <CartesianGrid vertical={false} />
          <XAxis
            dataKey="month"
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => value.slice(0, 3)}
          />
          <YAxis
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            tickFormatter={(value) => `${value}`}
            domain={[0, yAxisMax]}
          />
          <ChartTooltip 
            cursor={false} 
            content={<ChartTooltipContent 
              formatter={(value) => [
                `${value} shipments`,
                "Monthly Volume"
              ]}
              labelFormatter={(label) => `${label} 2024`}
            />} 
          />
          <defs>
            <linearGradient id="fillShipments" x1="0" y1="0" x2="0" y2="1">
              <stop
                offset="5%"
                stopColor="var(--color-shipments)"
                stopOpacity={0.8}
              />
              <stop
                offset="95%"
                stopColor="var(--color-shipments)"
                stopOpacity={0.1}
              />
            </linearGradient>
          </defs>
          <Area
            dataKey="shipments"
            type="natural"
            fill="url(#fillShipments)"
            fillOpacity={0.4}
            stroke="var(--color-shipments)"
            strokeWidth={2}
          />
        </AreaChart>
      </ChartContainer>
      
      {/* Trend indicator */}
      <div className="flex w-full items-start gap-2 text-sm mt-2">
        <div className="grid gap-2">
          <div className="flex items-center gap-2 font-medium leading-none">
            {hasIncrease ? "Trending up" : "Trending down"} by {trendPercentage}% this month 
            <TrendingUp className={`h-4 w-4 ${hasIncrease ? "text-green-500" : "text-red-500 rotate-180"}`} />
          </div>
          <div className="flex items-center gap-2 leading-none text-muted-foreground">
            Shipment volume over the last 6 months
          </div>
        </div>
      </div>
    </div>
  );
}
