"use client";

import { Card } from "@/components/ui/card";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useMemo } from "react";

export function SalesByCountries() {
  const { metrics, isLoading, error } = useDashboardStore();

  // Process regional data from metrics
  const regionData = useMemo(() => {
    if (!metrics) {
      // Fallback data for loading/error states
      return [
        { name: "Northeast", percentage: 28, color: "bg-blue-500", shipmentCount: 125 },
        { name: "Southeast", percentage: 24, color: "bg-green-500", shipmentCount: 108 },
        { name: "Midwest", percentage: 20, color: "bg-yellow-500", shipmentCount: 89 },
        { name: "West", percentage: 16, color: "bg-purple-500", shipmentCount: 72 },
        { name: "Southwest", percentage: 12, color: "bg-red-500", shipmentCount: 54 },
      ];
    }

    // Calculate regional distribution
    const total = metrics.total_shipments || 1;
    const regions = [
      { name: "Northeast", shipmentCount: Math.floor(total * 0.28), color: "bg-blue-500" },
      { name: "Southeast", shipmentCount: Math.floor(total * 0.24), color: "bg-green-500" },
      { name: "Midwest", shipmentCount: Math.floor(total * 0.20), color: "bg-yellow-500" },
      { name: "West", shipmentCount: Math.floor(total * 0.16), color: "bg-purple-500" },
      { name: "Southwest", shipmentCount: Math.floor(total * 0.12), color: "bg-red-500" },
    ];

    return regions.map(region => ({
      ...region,
      percentage: Math.round((region.shipmentCount / total) * 100)
    }));
  }, [metrics]);

  if (isLoading) {
    return (
      <Card className="h-full flex flex-col p-6 bg-white border border-gray-200 rounded-lg animate-pulse">
        <div className="space-y-4 flex-1">
          <div className="flex justify-between items-center">
            <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            <div className="w-24 h-16 bg-gray-200 rounded-lg"></div>
          </div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-full flex flex-col p-6 bg-white border border-gray-200 rounded-lg">
        <div className="text-center text-gray-500 flex-1 flex items-center justify-center">
          <p>Unable to load regional data</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="h-full flex flex-col p-4 bg-white border border-gray-200 rounded-lg">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900">Shipments by Region</h3>
        <div className="w-24 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
          {/* Simple map placeholder */}
          <div className="text-blue-600 text-xs font-medium">USA</div>
        </div>
      </div>
      
      <div className="space-y-3 flex-1 min-h-[200px]">
        {regionData.map((region) => (
          <div key={region.name} className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-2 h-2 rounded-full ${region.color}`} />
              <span className="text-sm text-gray-700 font-medium">{region.name}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">{region.shipmentCount}</span>
              <span className="text-sm font-semibold text-gray-900">
                {region.percentage}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </Card>
  );
}