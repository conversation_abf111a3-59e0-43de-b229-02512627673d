"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { Shipment } from "@/lib/types";
import { format } from "date-fns";
import { useState, useEffect } from "react";
import { ShipmentDetailSheet } from "@/components/dashboard/shipment-detail-sheet";
import { Package, Truck, MapPin, Clock, AlertTriangle, ExternalLink, ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";

export function DashboardShipments() {
  const context = useOrganizationContext();
  const { recentShipments, isLoading, error, fetchShipments } = useDashboardStore();
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 6; // Show 6 shipments per page

  useEffect(() => {
    if (context?.apiClient) {
      fetchShipments(context.apiClient);
    }
  }, [context?.apiClient, fetchShipments]);

  const getStatusColor = (status: string) => {
    switch (status?.toLowerCase()) {
      case "delivered":
        return "bg-green-100 text-green-800 border-green-200";
      case "in transit":
      case "in_transit":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "delayed":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "exception":
      case "failed":
        return "bg-red-100 text-red-800 border-red-200";
      case "pending":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status?.toLowerCase()) {
      case "delivered":
        return <Package className="w-3 h-3" />;
      case "in transit":
      case "in_transit":
        return <Truck className="w-3 h-3" />;
      case "delayed":
        return <Clock className="w-3 h-3" />;
      case "exception":
      case "failed":
        return <AlertTriangle className="w-3 h-3" />;
      default:
        return <MapPin className="w-3 h-3" />;
    }
  };

  if (isLoading) {
    return (
      <Card className="p-4 bg-white border border-gray-200 rounded-lg h-full flex flex-col animate-pulse">
        <div className="space-y-4 flex-1">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 bg-white border border-red-200 rounded-lg h-full flex flex-col">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Recent Shipments</h3>
          <p className="text-sm text-red-600">Error loading shipments: {error}</p>
        </div>
      </Card>
    );
  }

  const allShipments = Array.isArray(recentShipments) ? recentShipments : [];
  const totalPages = Math.ceil(allShipments.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const shipmentsToShow = allShipments.slice(startIndex, endIndex);

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  return (
    <Card className="p-4 bg-white border border-gray-200 rounded-lg max-h-[600px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Recent Shipments</h3>
          <p className="text-sm text-gray-500">Latest shipment activity</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            {allShipments.length} total
          </Badge>
          <Link href="/dashboard/shipments">
            <Button variant="ghost" size="sm" className="h-7 text-xs">
              <ExternalLink className="w-3 h-3 mr-1" />
              View All
            </Button>
          </Link>
        </div>
      </div>
      
      <div className="flex-1 min-h-[200px] max-h-[400px] overflow-y-auto">
        {shipmentsToShow.length === 0 ? (
          <div className="text-center py-8 flex-1 flex flex-col items-center justify-center">
            <Package className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">No recent shipments</p>
          </div>
        ) : (
          <div className="space-y-2">
            {shipmentsToShow.map((shipment: Shipment) => (
              <div 
                key={shipment.id} 
                className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <div className="flex-shrink-0">
                    {getStatusIcon(shipment.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Button 
                        variant="link" 
                        className="h-auto p-0 text-primary font-medium text-sm"
                        onClick={() => {
                          setSelectedShipment(shipment);
                          setIsSheetOpen(true);
                        }}
                      >
                        {shipment.tracking_number || shipment.external_reference_number || `SHIP-${shipment.id}`}
                      </Button>
                      <Badge 
                        variant="outline" 
                        className={`text-xs ${getStatusColor(shipment.status)}`}
                      >
                        {shipment.status}
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-500 truncate">
                      {shipment.origin_address && shipment.destination_address ? (
                        <>
                          <span className="font-medium">From:</span> {shipment.origin_address?.split(',')[0]} 
                          <span className="mx-2">→</span>
                          <span className="font-medium">To:</span> {shipment.destination_address?.split(',')[0]}
                        </>
                      ) : (
                        <span>Route information not available</span>
                      )}
                    </div>
                  </div>
                </div>
                <div className="text-right text-xs text-gray-500 flex-shrink-0 ml-2">
                  <div>
                    {shipment.expected_delivery_date 
                      ? format(new Date(shipment.expected_delivery_date), "MMM dd")
                      : "TBD"}
                  </div>
                  {shipment.carrier?.full_name && (
                    <div className="text-gray-400 truncate max-w-[80px]">
                      {shipment.carrier.full_name}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between pt-4 border-t border-gray-100 mt-4">
          <div className="text-xs text-gray-500">
            Page {currentPage} of {totalPages} • Showing {shipmentsToShow.length} of {allShipments.length}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={goToPrevPage}
              disabled={currentPage === 1}
              className="h-8 w-8 p-0"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="text-xs text-gray-500 min-w-[40px] text-center">
              {currentPage}/{totalPages}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={goToNextPage}
              disabled={currentPage === totalPages}
              className="h-8 w-8 p-0"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Sheet component for showing shipment details */}
      <ShipmentDetailSheet 
        shipment={selectedShipment} 
        isOpen={isSheetOpen} 
        onOpenChange={setIsSheetOpen} 
      />
    </Card>
  );
}