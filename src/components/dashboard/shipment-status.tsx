"use client";

import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, Responsive<PERSON><PERSON><PERSON>, <PERSON>, Tooltip } from "recharts";

interface ShipmentStatusProps {
  data: Array<{ name: string; value: number }>;
}

const COLORS = ["#3b82f6", "#22c55e", "#f59e0b", "#ef4444", "#8b5cf6"];

export function ShipmentStatus({ data }: ShipmentStatusProps) {
  // Filter out zero values to avoid showing empty segments
  const filteredData = data.filter(item => item.value > 0);
  
  // Calculate the total for percentage display
  const total = filteredData.reduce((sum, item) => sum + item.value, 0);
  
  // Custom label formatter to show percentages
  const renderCustomLabel = ({ name, value }: { name: string; value: number }) => {
    const percentage = ((value / total) * 100).toFixed(0);
    return percentage !== "0" ? `${name}: ${percentage}%` : "";
  };
  
  return (
    <div className="h-[300px]">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={filteredData}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            dataKey="value"
            label={renderCustomLabel}
            animationDuration={800}
          >
            {filteredData.map((entry, index) => (
              <Cell 
                key={`cell-${index}`} 
                fill={COLORS[index % COLORS.length]} 
                stroke="var(--background)" 
                strokeWidth={2} 
              />
            ))}
          </Pie>
          <Tooltip 
            formatter={(value) => [`${value} shipments`]}
            contentStyle={{ 
              backgroundColor: "var(--background)",
              borderColor: "var(--border)",
              borderRadius: "0.5rem"
            }}
          />
          <Legend 
            layout="horizontal"
            verticalAlign="bottom"
            align="center"
          />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
}
