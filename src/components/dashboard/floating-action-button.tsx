"use client";

import { useState } from "react";
import { Plus, Package, MapPin, Phone, FileText, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";

interface ActionItem {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  action: () => void;
  color: string;
}

export function FloatingActionButton() {
  const [isOpen, setIsOpen] = useState(false);

  const actions: ActionItem[] = [
    {
      icon: Package,
      label: "New Shipment",
      action: () => console.log("New Shipment"),
      color: "from-blue-500 to-indigo-500"
    },
    {
      icon: MapPin,
      label: "Track Package",
      action: () => console.log("Track Package"),
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: FileText,
      label: "Create Quote",
      action: () => console.log("Create Quote"),
      color: "from-purple-500 to-pink-500"
    },
    {
      icon: Phone,
      label: "Voice Call",
      action: () => console.log("Voice Call"),
      color: "from-orange-500 to-red-500"
    }
  ];

  return (
    <TooltipProvider>
      <div className="fixed bottom-6 right-6 z-50">
        {/* Action Items */}
        <div className={cn(
          "flex flex-col gap-3 mb-4 transition-all duration-300 transform",
          isOpen ? "opacity-100 scale-100 translate-y-0" : "opacity-0 scale-95 translate-y-4 pointer-events-none"
        )}>
          {actions.map((action, index) => (
            <Tooltip key={action.label}>
              <TooltipTrigger asChild>
                <Button
                  size="icon"
                  className={cn(
                    "w-12 h-12 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border-0",
                    `bg-gradient-to-r ${action.color} hover:scale-110`
                  )}
                  style={{
                    animationDelay: `${index * 50}ms`
                  }}
                  onClick={() => {
                    action.action();
                    setIsOpen(false);
                  }}
                >
                  <action.icon className="h-5 w-5 text-white" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="left" className="glass-card bg-white/90 dark:bg-slate-800/90 border border-white/20 dark:border-slate-700/50">
                <p className="font-medium">{action.label}</p>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>

        {/* Main FAB */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              size="icon"
              className={cn(
                "w-14 h-14 rounded-full shadow-xl hover:shadow-2xl transition-all duration-300 border-0",
                "bg-gradient-to-r from-blue-600 via-purple-600 to-teal-500 hover:scale-110",
                isOpen && "rotate-45"
              )}
              onClick={() => setIsOpen(!isOpen)}
            >
              {isOpen ? (
                <X className="h-6 w-6 text-white transition-transform duration-300" />
              ) : (
                <Plus className="h-6 w-6 text-white transition-transform duration-300" />
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent side="left" className="glass-card bg-white/90 dark:bg-slate-800/90 border border-white/20 dark:border-slate-700/50">
            <p className="font-medium">{isOpen ? "Close" : "Quick Actions"}</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
}
