"use client";

import { useState, useEffect } from "react";
import { 
  Card, 
  CardContent,
  CardDescription, 
  CardHeader, 
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Shipment } from "@/lib/types";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { format } from "date-fns";
import { Download, Share2, Truck, MapPin, Calendar, Package, RefreshCcw, AlertCircle } from "lucide-react";


interface ShipmentDetailSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  shipment: Shipment | null; // Changed to pass shipment directly
}

interface TrackingDetails {
  tracking_number: string;
  carrier: string;
  status: string;
  origin: string;
  destination: string;
  estimated_delivery: string | null;
  updates: Array<{
    timestamp: string;
    location: string;
    status: string;
    details: string;
  }>;
}

export function ShipmentDetailSheet({ isOpen, onOpenChange, shipment }: ShipmentDetailSheetProps) {
  const [trackingDetails, setTrackingDetails] = useState<TrackingDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { apiClient } = useOrganizationContext();
  const { fetchTrackingDetails } = useDashboardStore();

  useEffect(() => {
    async function fetchDetailedTracking() {
      if (!isOpen || !shipment?.tracking_number || !apiClient) {
        setTrackingDetails(null);
        return;
      }
      
      setIsLoading(true);
      setError(null);
      
      try {
        console.log(`🔍 Fetching detailed tracking for: ${shipment.tracking_number}`);
        
        // Use the new dashboard store method to get tracking details
        const details = await fetchTrackingDetails(apiClient, shipment.tracking_number);
        
        if (details) {
          console.log(`✅ Successfully fetched tracking details:`, details);
          setTrackingDetails(details);
          setError(null);
        } else {
          console.warn(`⚠️ No tracking details found for: ${shipment.tracking_number}`);
          setError('No tracking details available for this shipment');
          setTrackingDetails(null);
        }
        
      } catch (apiError: unknown) {
        const errorMessage = apiError instanceof Error ? apiError.message : 'Unknown error occurred';
        console.error(`❌ Failed to fetch tracking details for ${shipment.tracking_number}:`, errorMessage);
        setError(`Failed to load tracking details: ${errorMessage}`);
        setTrackingDetails(null);
      } finally {
        setIsLoading(false);
      }
    }

    fetchDetailedTracking();
  }, [isOpen, shipment?.tracking_number, apiClient, fetchTrackingDetails]);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return "bg-green-500";
      case "in transit":
        return "bg-blue-500";
      case "delayed":
        return "bg-amber-500";
      case "exception":
      case "failed":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="w-full md:max-w-xl lg:max-w-2xl overflow-y-auto" side="right">
        {!shipment ? (
          <div className="flex flex-col items-center justify-center h-full">
            <AlertCircle className="h-8 w-8 text-destructive mb-4" />
            <h3 className="text-lg font-semibold mb-2">No Shipment Selected</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              Please select a shipment to view details
            </p>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          </div>
        ) : (
          <>
            <SheetHeader>
              <SheetTitle>Shipment {shipment.tracking_number}</SheetTitle>
              <SheetDescription>
                View shipment details and tracking history
              </SheetDescription>
            </SheetHeader>
            
            <div className="my-4">
              <div className="flex items-center justify-between mb-4">
                <Badge className={getStatusColor(shipment.status)}>
                  {shipment.status}
                </Badge>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm">
                    <Share2 className="mr-1 h-4 w-4" /> Share
                  </Button>
                  <Button variant="outline" size="sm">
                    <Download className="mr-1 h-4 w-4" /> Export
                  </Button>
                </div>
              </div>
              
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle>Shipment Information</CardTitle>
                  <CardDescription>Details about this shipment</CardDescription>
                </CardHeader>
                <CardContent className="grid gap-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium mb-1">Tracking Number</p>
                      <p className="text-sm">{shipment.tracking_number}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium mb-1">Reference Number</p>
                      <p className="text-sm">{shipment.external_reference_number || "N/A"}</p>
                    </div>
                  </div>
                  
                  <div className="border-t pt-4">
                    <div className="grid grid-cols-2 gap-4 mb-4">
                      <div className="flex gap-2">
                        <MapPin className="h-5 w-5 text-muted-foreground shrink-0" />
                        <div>
                          <p className="text-sm font-medium">Origin</p>
                          <p className="text-sm text-muted-foreground">{shipment.origin_address || "N/A"}</p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <MapPin className="h-5 w-5 text-muted-foreground shrink-0" />
                        <div>
                          <p className="text-sm font-medium">Destination</p>
                          <p className="text-sm text-muted-foreground">{shipment.destination_address || "N/A"}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="border-t pt-4">
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-2 items-center">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <div className="text-sm">
                          <span className="font-medium">Expected Delivery: </span>
                          {shipment.expected_delivery_date 
                            ? format(new Date(shipment.expected_delivery_date), "MMMM dd, yyyy")
                            : "N/A"}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <Truck className="h-4 w-4 text-muted-foreground" />
                        <div className="text-sm">
                          <span className="font-medium">Carrier: </span>
                          {shipment.carrier?.full_name || "N/A"}
                        </div>
                      </div>
                      <div className="flex gap-2 items-center">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <div className="text-sm">
                          <span className="font-medium">Type: </span>
                          {shipment.shipment_type || "General"}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="mt-4">
                <CardHeader className="pb-2">
                  <CardTitle>Tracking History</CardTitle>
                  <CardDescription>
                    {isLoading ? (
                      <span className="flex items-center gap-2">
                        <RefreshCcw className="h-3 w-3 animate-spin" />
                        Loading tracking details...
                      </span>
                    ) : trackingDetails && trackingDetails.updates.length > 0 ? (
                      `Latest update: ${format(new Date(trackingDetails.updates[0].timestamp), "MMM dd, yyyy 'at' h:mm a")}`
                    ) : (
                      "No tracking updates available"
                    )}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="relative">
                    {error && (
                      <div className="text-sm text-destructive py-4 text-center bg-destructive/10 rounded-md mb-4">
                        {error}
                      </div>
                    )}
                    {isLoading ? (
                      <div className="flex items-center justify-center py-8">
                        <RefreshCcw className="h-6 w-6 animate-spin text-primary mr-2" />
                        <span className="text-sm text-muted-foreground">Loading tracking details...</span>
                      </div>
                    ) : !trackingDetails || trackingDetails.updates.length === 0 ? (
                      <p className="text-sm text-muted-foreground py-8 text-center">No tracking updates available</p>
                    ) : (
                      <div className="space-y-8">
                        {trackingDetails.updates.map((update, index) => (
                          <div key={`${update.timestamp}-${index}`} className="relative flex gap-6">
                            <div className="flex items-center justify-center">
                              <div className="h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center">
                                <div className="h-5 w-5 rounded-full bg-primary" />
                              </div>
                              {index < trackingDetails.updates.length - 1 && (
                                <div className="absolute top-10 bottom-0 left-5 -ml-px w-0.5 bg-muted-foreground/20" />
                              )}
                            </div>
                            <div className="flex flex-col pb-8">
                              <span className="text-sm font-medium">{update.status}</span>
                              <span className="text-xs text-muted-foreground mb-2">
                                {format(new Date(update.timestamp), "MMM dd, yyyy 'at' h:mm a")}
                              </span>
                              <span className="text-sm">{update.location || "N/A"}</span>
                              {update.details && <p className="text-sm text-muted-foreground mt-1">{update.details}</p>}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </SheetContent>
    </Sheet>
  );
}

