"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Line, XAxis, <PERSON>Axis, CartesianGrid, ResponsiveContainer, Tooltip } from "recharts";
import { useDashboardStore } from "@/lib/stores/dashboard-store";

export function ProductSalesChart() {
  const { chartData, revenue, metrics, isLoading, error } = useDashboardStore();

  if (isLoading) {
    return (
      <Card className="p-6 bg-white border border-gray-200 rounded-lg animate-pulse h-full flex flex-col">
        <div className="space-y-4 flex-1">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="flex-1 bg-gray-200 rounded min-h-[300px]"></div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-6 bg-white border border-red-200 rounded-lg h-full flex flex-col">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Shipment Trends</h3>
          <p className="text-sm text-red-600">Error loading chart data: {error}</p>
        </div>
      </Card>
    );
  }

  // Transform chart data for Recharts
  const transformedData = chartData.labels.map((label, index) => ({
    date: label,
    revenue: chartData.datasets[0]?.data[index] || 0,
  }));

  const totalRevenue = revenue?.total_revenue || 0;

  return (
    <Card className="p-4 bg-white border border-gray-200 rounded-lg h-full flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Revenue Trends</h3>
          <p className="text-sm text-gray-500">Daily revenue over time</p>
        </div>
        <div className="text-right">
          <div className="text-xl font-semibold text-gray-900">
            ${totalRevenue.toLocaleString()}
          </div>
          <div className="text-xs text-gray-500">{metrics?.period_days || 30} days</div>
        </div>
      </div>
      
      <div className="flex-1 min-h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart
            data={transformedData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
            <XAxis 
              dataKey="date" 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12, fill: '#6b7280' }}
              tickFormatter={(value) => {
                if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`;
                if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`;
                return `$${value}`;
              }}
            />
            <Tooltip 
              formatter={(value: number) => [`$${value.toLocaleString()}`, 'Revenue']}
              labelFormatter={(label) => `Date: ${label}`}
            />
            <Line 
              type="monotone"
              dataKey="revenue" 
              stroke="#3b82f6" 
              strokeWidth={2}
              dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
              activeDot={{ r: 6 }}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
}