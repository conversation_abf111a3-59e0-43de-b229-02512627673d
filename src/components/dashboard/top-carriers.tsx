"use client";

import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useEffect } from "react";
import { Truck, Star, Phone, Mail, ExternalLink, Award, Clock } from "lucide-react";
import Link from "next/link";

export function TopCarriers() {
  const context = useOrganizationContext();
  const { topCarriers, isLoading, error, fetchTopCarriers } = useDashboardStore();

  useEffect(() => {
    if (context?.apiClient) {
      fetchTopCarriers(context.apiClient);
    }
  }, [context?.apiClient, fetchTopCarriers]);

  const getRatingColor = (rating: number) => {
    if (rating >= 4.5) return "text-green-600";
    if (rating >= 4.0) return "text-blue-600";
    if (rating >= 3.5) return "text-amber-600";
    return "text-red-600";
  };

  const getPerformanceBadge = (onTimePercentage: number) => {
    if (onTimePercentage >= 95) return { text: "Excellent", class: "bg-green-100 text-green-800 border-green-200" };
    if (onTimePercentage >= 85) return { text: "Good", class: "bg-blue-100 text-blue-800 border-blue-200" };
    if (onTimePercentage >= 75) return { text: "Average", class: "bg-amber-100 text-amber-800 border-amber-200" };
    return { text: "Needs Improvement", class: "bg-red-100 text-red-800 border-red-200" };
  };

  if (isLoading) {
    return (
      <Card className="p-4 bg-white border border-gray-200 rounded-lg max-h-[600px] flex flex-col animate-pulse">
        <div className="space-y-4 flex-1">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="p-4 border border-gray-100 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <div className="h-5 bg-gray-200 rounded w-1/2"></div>
                  <div className="h-6 bg-gray-200 rounded w-16"></div>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="p-4 bg-white border border-red-200 rounded-lg max-h-[600px] flex flex-col">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Top Carriers</h3>
          <p className="text-sm text-red-600">Error loading carriers: {error}</p>
        </div>
      </Card>
    );
  }

  const carriersToShow = Array.isArray(topCarriers) ? topCarriers.slice(0, 5) : [];

  return (
    <Card className="p-4 bg-white border border-gray-200 rounded-lg max-h-[600px] flex flex-col">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900">Top Performing Carriers</h3>
          <p className="text-sm text-gray-500">Best carriers in the last 30 days</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="secondary" className="text-xs">
            <Award className="w-3 h-3 mr-1" />
            Top 5
          </Badge>
          <Link href="/dashboard/carriers">
            <Button variant="ghost" size="sm" className="h-7 text-xs">
              <ExternalLink className="w-3 h-3 mr-1" />
              View All
            </Button>
          </Link>
        </div>
      </div>
      
      <div className="flex-1 min-h-[200px] max-h-[400px] overflow-y-auto">
        {carriersToShow.length === 0 ? (
          <div className="text-center py-8 flex-1 flex flex-col items-center justify-center">
            <Truck className="w-12 h-12 text-gray-400 mx-auto mb-3" />
            <p className="text-gray-500 text-sm">No carrier data available</p>
          </div>
        ) : (
          <div className="space-y-3">
            {carriersToShow.map((carrier, index) => {
              const performanceBadge = getPerformanceBadge(carrier.on_time_percentage);
              
              return (
                <div 
                  key={carrier.id} 
                  className="p-4 hover:bg-gray-50 rounded-lg transition-colors border border-gray-100"
                >
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-full">
                        <span className="text-blue-600 font-bold text-sm">#{index + 1}</span>
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 text-sm">{carrier.name}</h4>
                        <div className="flex items-center gap-2 mt-1">
                          <div className="flex items-center gap-1">
                            <Star className={`w-3 h-3 ${getRatingColor(carrier.average_rating)}`} />
                            <span className={`text-xs font-medium ${getRatingColor(carrier.average_rating)}`}>
                              {carrier.average_rating.toFixed(1)}
                            </span>
                          </div>
                          <Badge 
                            variant="outline" 
                            className={`text-xs ${performanceBadge.class}`}
                          >
                            <Clock className="w-2 h-2 mr-1" />
                            {performanceBadge.text}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-semibold text-gray-900">
                        ${carrier.total_revenue.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500">Revenue</div>
                    </div>
                  </div>

                  <div className="grid grid-cols-3 gap-4 text-xs">
                    <div className="text-center">
                      <div className="font-semibold text-gray-900">{carrier.total_shipments}</div>
                      <div className="text-gray-500">Shipments</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-green-600">{carrier.on_time_percentage.toFixed(1)}%</div>
                      <div className="text-gray-500">On-Time</div>
                    </div>
                    <div className="text-center">
                      <div className="font-semibold text-blue-600">
                        ${(carrier.total_revenue / carrier.total_shipments).toLocaleString()}
                      </div>
                      <div className="text-gray-500">Avg/Shipment</div>
                    </div>
                  </div>

                  {(carrier.contact_email || carrier.phone) && (
                    <div className="flex items-center gap-4 mt-3 pt-3 border-t border-gray-100">
                      {carrier.contact_email && (
                        <div className="flex items-center gap-1 text-xs text-gray-600">
                          <Mail className="w-3 h-3" />
                          <span className="truncate max-w-[120px]">{carrier.contact_email}</span>
                        </div>
                      )}
                      {carrier.phone && (
                        <div className="flex items-center gap-1 text-xs text-gray-600">
                          <Phone className="w-3 h-3" />
                          <span>{carrier.phone}</span>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>
    </Card>
  );
}