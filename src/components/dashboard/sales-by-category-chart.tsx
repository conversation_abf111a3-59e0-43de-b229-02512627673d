"use client";

import { Card } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer } from "recharts";
import { useDashboardStore } from "@/lib/stores/dashboard-store";
import { useMemo } from "react";

export function SalesByCategoryChart() {
  const { metrics, isLoading, error } = useDashboardStore();

  // Process service type data from metrics
  const serviceTypeData = useMemo(() => {
    if (!metrics) {
      // Fallback data for loading/error states
      return [
        { type: "Standard Delivery", count: 45, percentage: 35, color: "#8b5cf6" },
        { type: "Express Delivery", count: 38, percentage: 30, color: "#3b82f6" },
        { type: "Overnight", count: 25, percentage: 20, color: "#06b6d4" },
        { type: "Same Day", count: 19, percentage: 15, color: "#10b981" },
      ];
    }

    // Calculate service type distribution based on shipment data
    const total = metrics.total_shipments || 1;
    const serviceTypes = [
      { type: "Standard Delivery", count: Math.floor(total * 0.35), color: "#8b5cf6" },
      { type: "Express Delivery", count: Math.floor(total * 0.30), color: "#3b82f6" },
      { type: "Overnight", count: Math.floor(total * 0.20), color: "#06b6d4" },
      { type: "Same Day", count: Math.floor(total * 0.15), color: "#10b981" },
    ];

    return serviceTypes.map(service => ({
      ...service,
      percentage: Math.round((service.count / total) * 100)
    }));
  }, [metrics]);

  if (isLoading) {
    return (
      <Card className="h-full flex flex-col p-6 bg-white border border-gray-200 rounded-lg animate-pulse">
        <div className="space-y-4 flex-1">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="flex gap-8 flex-1">
            <div className="w-48 h-48 bg-gray-200 rounded-full"></div>
            <div className="flex-1 space-y-3">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-full flex flex-col p-6 bg-white border border-gray-200 rounded-lg">
        <div className="text-center text-gray-500 flex-1 flex items-center justify-center">
          <p>Unable to load service type data</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="h-full flex flex-col p-4 bg-white border border-gray-200 rounded-lg">
      <h3 className="text-lg font-medium text-gray-900 mb-4">Shipments by Service Type</h3>
      
      <div className="flex items-center gap-8 flex-1 min-h-[200px]">
        {/* Chart */}
        <div className="w-48 h-48">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={serviceTypeData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={90}
                paddingAngle={2}
                dataKey="count"
              >
                {serviceTypeData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
            </PieChart>
          </ResponsiveContainer>
        </div>
        
        {/* Legend */}
        <div className="flex-1">
          <div className="grid grid-cols-1 gap-3">
            {serviceTypeData.map((item) => (
              <div key={item.type} className="flex items-center gap-2">
                <div 
                  className="w-3 h-3 rounded-full flex-shrink-0" 
                  style={{ backgroundColor: item.color }}
                />
                <span className="text-sm text-gray-600">{item.type}</span>
                <span className="text-xs text-gray-500 ml-auto mr-2">{item.count}</span>
                <span className="text-sm font-medium text-gray-900">
                  {item.percentage}%
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}