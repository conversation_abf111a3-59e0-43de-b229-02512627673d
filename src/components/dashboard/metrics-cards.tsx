"use client";

import { Card } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Plus, Package, Truck, AlertCircle, CheckCircle, Phone, Users } from "lucide-react";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useDashboardStore } from "@/lib/stores/dashboard-store";

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  isPositive: boolean;
  icon: React.ComponentType<{ className?: string }>;
  showAddButton?: boolean;
  actionable?: boolean;
  needsAttention?: boolean;
}

function MetricCard({ title, value, change, isPositive, icon: Icon, showAddButton, actionable, needsAttention }: MetricCardProps) {
  return (
    <Card className={cn(
      "p-6 border rounded-lg transition-all hover:shadow-md h-full flex flex-col",
      needsAttention 
        ? "bg-gradient-to-br from-red-50 to-orange-50 border-red-200" 
        : actionable 
        ? "bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200" 
        : "bg-white border-gray-200"
    )}>
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-2">
            <Icon className={cn(
              "h-5 w-5",
              needsAttention ? "text-red-600" : actionable ? "text-blue-600" : "text-gray-500"
            )} />
            <p className="text-sm font-medium text-gray-600">{title}</p>
            {needsAttention && (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
            {actionable && !needsAttention && (
              <CheckCircle className="h-4 w-4 text-blue-500" />
            )}
          </div>
          <div className="flex items-baseline gap-2">
            <h3 className="text-2xl font-bold text-gray-900">{value}</h3>
            <div className={cn(
              "flex items-center gap-1 text-sm font-medium",
              isPositive ? "text-green-600" : "text-red-600"
            )}>
              {isPositive ? (
                <TrendingUp className="h-4 w-4" />
              ) : (
                <TrendingDown className="h-4 w-4" />
              )}
              <span>{change}</span>
            </div>
          </div>
        </div>
        {showAddButton && (
          <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
            <Plus className="h-5 w-5" />
          </button>
        )}
      </div>
    </Card>
  );
}

interface MetricsCardsProps {
  index?: number; // For individual card rendering in bento grid
}

export function MetricsCards({ index }: MetricsCardsProps = {}) {
  const context = useOrganizationContext();
  const { metrics, performance, isLoading, error } = useDashboardStore();

  useEffect(() => {
    if (context?.apiClient) {
      // The dashboard store should already be fetching data
      // This component just consumes the data from the store
    }
  }, [context?.apiClient]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // Calculate if metrics need attention
  const delayedShipments = metrics?.delayed_shipments || 0;
  const inTransitShipments = metrics?.in_transit_shipments || 0;
  
  // Voice and AI metrics (mock data for now - should come from actual voice API)
  const voiceMetrics = {
    total_calls: 247,
    active_calls: 12,
    avg_call_duration: 3.4, // minutes
    ai_assist_rate: 85.2, // percentage of calls with AI assistance
    customer_satisfaction: 4.6, // out of 5
    call_resolution_rate: 92.3, // percentage
  };

  const metricsData = [
    {
      title: "Active Voice Calls",
      value: formatNumber(voiceMetrics.active_calls),
      change: `${voiceMetrics.total_calls} total today`,
      isPositive: true,
      icon: Phone,
      actionable: voiceMetrics.active_calls > 5,
      needsAttention: voiceMetrics.active_calls > 20,
    },
    {
      title: "Customer Satisfaction",
      value: `${voiceMetrics.customer_satisfaction.toFixed(1)}/5`,
      change: `${voiceMetrics.call_resolution_rate.toFixed(1)}% resolved`,
      isPositive: voiceMetrics.customer_satisfaction >= 4.0,
      icon: Users,
      actionable: voiceMetrics.customer_satisfaction >= 4.5,
      needsAttention: voiceMetrics.customer_satisfaction < 3.5,
    },
    {
      title: "Total Shipments",
      value: formatNumber(metrics?.total_shipments || 0),
      change: `${performance?.shipment_growth_rate?.toFixed(1) || 0}% growth`,
      isPositive: (performance?.shipment_growth_rate || 0) >= 0,
      icon: Package,
      actionable: (performance?.shipment_growth_rate || 0) > 10,
    },
    {
      title: "In Transit",
      value: formatNumber(inTransitShipments),
      change: `${delayedShipments} delayed`,
      isPositive: delayedShipments === 0,
      icon: Truck,
      actionable: inTransitShipments > 0,
      needsAttention: delayedShipments > 2,
    },
  ];

  // If rendering individual card for bento grid
  if (typeof index === 'number') {
    if (isLoading) {
      return (
        <Card className="p-6 bg-white border border-gray-200 rounded-lg animate-pulse h-full">
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
          </div>
        </Card>
      );
    }

    if (error) {
      return (
        <Card className="p-6 bg-white border border-red-200 rounded-lg h-full">
          <p className="text-sm text-red-600">Error loading metrics</p>
        </Card>
      );
    }

    const metric = metricsData[index];
    if (!metric) return null;

    return (
      <MetricCard
        title={metric.title}
        value={metric.value}
        change={metric.change}
        isPositive={metric.isPositive}
        icon={metric.icon}
        showAddButton={index === metricsData.length - 1}
        actionable={metric.actionable}
        needsAttention={metric.needsAttention}
      />
    );
  }

  // Legacy grid rendering (fallback)
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="p-6 bg-white border border-gray-200 rounded-lg animate-pulse">
            <div className="space-y-3">
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6 bg-white border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">Error loading metrics: {error}</p>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {metricsData.map((metric, idx) => (
        <MetricCard
          key={metric.title}
          title={metric.title}
          value={metric.value}
          change={metric.change}
          isPositive={metric.isPositive}
          icon={metric.icon}
          showAddButton={idx === metricsData.length - 1}
          actionable={metric.actionable}
          needsAttention={metric.needsAttention}
        />
      ))}
    </div>
  );
}