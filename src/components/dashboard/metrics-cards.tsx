"use client";

import { Card } from "@/components/ui/card";
import { TrendingUp, TrendingDown, Plus, Package, Truck, AlertCircle, CheckCircle, Phone, Users, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useDashboardStore } from "@/lib/stores/dashboard-store";

interface MetricCardProps {
  title: string;
  value: string;
  change: string;
  isPositive: boolean;
  icon: React.ComponentType<{ className?: string }>;
  showAddButton?: boolean;
  actionable?: boolean;
  needsAttention?: boolean;
}

function MetricCard({ title, value, change, isPositive, icon: Icon, showAddButton, actionable, needsAttention }: MetricCardProps) {
  return (
    <Card className={cn(
      "group relative p-6 border-0 rounded-2xl transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl h-full flex flex-col overflow-hidden",
      needsAttention
        ? "glass-card bg-gradient-to-br from-red-50/80 to-orange-50/80 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200/50 dark:border-red-800/50"
        : actionable
        ? "glass-card bg-gradient-to-br from-blue-50/80 to-indigo-50/80 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200/50 dark:border-blue-800/50"
        : "glass-card bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50"
    )}>
      {/* Gradient overlay for enhanced visual appeal */}
      <div className="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent pointer-events-none" />

      <div className="relative flex items-start justify-between">
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-4">
            <div className={cn(
              "p-3 rounded-xl transition-all duration-300 group-hover:scale-110",
              needsAttention
                ? "bg-gradient-to-br from-red-500 to-orange-500 shadow-lg shadow-red-500/25"
                : actionable
                ? "bg-gradient-to-br from-blue-500 to-indigo-500 shadow-lg shadow-blue-500/25"
                : "bg-gradient-to-br from-slate-400 to-gray-500 shadow-lg shadow-slate-500/25"
            )}>
              <Icon className="h-5 w-5 text-white" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-semibold text-slate-700 dark:text-slate-300">{title}</p>
              <div className="flex items-center gap-2 mt-1">
                {needsAttention && (
                  <AlertCircle className="h-3 w-3 text-red-500" />
                )}
                {actionable && !needsAttention && (
                  <CheckCircle className="h-3 w-3 text-blue-500" />
                )}
                {(needsAttention || actionable) && (
                  <span className="text-xs text-slate-500 dark:text-slate-400">
                    {needsAttention ? "Needs attention" : "Action available"}
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="space-y-2">
            <h3 className="text-3xl font-bold text-slate-900 dark:text-slate-100 tracking-tight">{value}</h3>
            <div className={cn(
              "flex items-center gap-2 text-sm font-medium px-3 py-1 rounded-full w-fit",
              isPositive
                ? "bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-400"
                : "bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-400"
            )}>
              {isPositive ? (
                <TrendingUp className="h-3 w-3" />
              ) : (
                <TrendingDown className="h-3 w-3" />
              )}
              <span>{change}</span>
            </div>
          </div>
        </div>
        {showAddButton && (
          <button className="p-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300 hover:bg-white/50 dark:hover:bg-slate-700/50 rounded-xl transition-all duration-300 hover:scale-110">
            <Plus className="h-5 w-5" />
          </button>
        )}
      </div>
    </Card>
  );
}

interface MetricsCardsProps {
  index?: number; // For individual card rendering in bento grid
}

export function MetricsCards({ index }: MetricsCardsProps = {}) {
  const context = useOrganizationContext();
  const { metrics, performance, isLoading, error } = useDashboardStore();

  useEffect(() => {
    if (context?.apiClient) {
      // The dashboard store should already be fetching data
      // This component just consumes the data from the store
    }
  }, [context?.apiClient]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  // Calculate if metrics need attention
  const delayedShipments = metrics?.delayed_shipments || 0;
  const inTransitShipments = metrics?.in_transit_shipments || 0;
  
  // Voice and AI metrics (mock data for now - should come from actual voice API)
  const voiceMetrics = {
    total_calls: 247,
    active_calls: 12,
    avg_call_duration: 3.4, // minutes
    ai_assist_rate: 85.2, // percentage of calls with AI assistance
    customer_satisfaction: 4.6, // out of 5
    call_resolution_rate: 92.3, // percentage
  };

  const metricsData = [
    {
      title: "Active Voice Calls",
      value: formatNumber(voiceMetrics.active_calls),
      change: `${voiceMetrics.total_calls} total today`,
      isPositive: true,
      icon: Phone,
      actionable: voiceMetrics.active_calls > 5,
      needsAttention: voiceMetrics.active_calls > 20,
    },
    {
      title: "Customer Satisfaction",
      value: `${voiceMetrics.customer_satisfaction.toFixed(1)}/5`,
      change: `${voiceMetrics.call_resolution_rate.toFixed(1)}% resolved`,
      isPositive: voiceMetrics.customer_satisfaction >= 4.0,
      icon: Users,
      actionable: voiceMetrics.customer_satisfaction >= 4.5,
      needsAttention: voiceMetrics.customer_satisfaction < 3.5,
    },
    {
      title: "Total Shipments",
      value: formatNumber(metrics?.total_shipments || 0),
      change: `${performance?.shipment_growth_rate?.toFixed(1) || 0}% growth`,
      isPositive: (performance?.shipment_growth_rate || 0) >= 0,
      icon: Package,
      actionable: (performance?.shipment_growth_rate || 0) > 10,
    },
    {
      title: "In Transit",
      value: formatNumber(inTransitShipments),
      change: `${delayedShipments} delayed`,
      isPositive: delayedShipments === 0,
      icon: Truck,
      actionable: inTransitShipments > 0,
      needsAttention: delayedShipments > 2,
    },
  ];

  // If rendering individual card for bento grid
  if (typeof index === 'number') {
    if (isLoading) {
      return (
        <Card className="p-6 bg-white border border-gray-200 rounded-lg animate-pulse h-full">
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
          </div>
        </Card>
      );
    }

    if (error) {
      return (
        <Card className="p-6 bg-white border border-red-200 rounded-lg h-full">
          <p className="text-sm text-red-600">Error loading metrics</p>
        </Card>
      );
    }

    const metric = metricsData[index];
    if (!metric) return null;

    return (
      <MetricCard
        title={metric.title}
        value={metric.value}
        change={metric.change}
        isPositive={metric.isPositive}
        icon={metric.icon}
        showAddButton={index === metricsData.length - 1}
        actionable={metric.actionable}
        needsAttention={metric.needsAttention}
      />
    );
  }

  // Enhanced loading state
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="glass-card p-6 bg-white/80 dark:bg-slate-800/80 border border-white/20 dark:border-slate-700/50 rounded-2xl animate-pulse">
            <div className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-slate-200 to-slate-300 dark:from-slate-600 dark:to-slate-700 rounded-xl"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-3 bg-slate-200 dark:bg-slate-600 rounded w-3/4"></div>
                  <div className="h-2 bg-slate-200 dark:bg-slate-600 rounded w-1/2"></div>
                </div>
              </div>
              <div className="space-y-2">
                <div className="h-8 bg-slate-200 dark:bg-slate-600 rounded w-2/3"></div>
                <div className="h-4 bg-slate-200 dark:bg-slate-600 rounded w-1/2"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="glass-card p-6 bg-gradient-to-br from-red-50/80 to-orange-50/80 dark:from-red-900/20 dark:to-orange-900/20 border border-red-200/50 dark:border-red-800/50 rounded-2xl col-span-full">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-gradient-to-r from-red-500 to-orange-500 rounded-xl">
              <AlertTriangle className="h-5 w-5 text-white" />
            </div>
            <div>
              <p className="font-medium text-red-700 dark:text-red-300">Error Loading Metrics</p>
              <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {metricsData.map((metric, idx) => (
        <MetricCard
          key={metric.title}
          title={metric.title}
          value={metric.value}
          change={metric.change}
          isPositive={metric.isPositive}
          icon={metric.icon}
          showAddButton={idx === metricsData.length - 1}
          actionable={metric.actionable}
          needsAttention={metric.needsAttention}
        />
      ))}
    </div>
  );
}