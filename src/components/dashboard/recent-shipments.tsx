"use client";

import { Shipment } from "@/lib/types";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { useState } from "react";
import { ShipmentDetailSheet } from "@/components/dashboard/shipment-detail-sheet";

interface RecentShipmentsProps {
  shipments: Shipment[];
}

export function RecentShipments({ shipments }: RecentShipmentsProps) {
  const [selectedShipment, setSelectedShipment] = useState<Shipment | null>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return "bg-green-500";
      case "in transit":
        return "bg-blue-500";
      case "delayed":
        return "bg-amber-500";
      case "exception":
      case "failed":
        return "bg-red-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="overflow-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Tracking #</TableHead>
            <TableHead>Origin</TableHead>
            <TableHead>Destination</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Carrier</TableHead>
            <TableHead>Est. Delivery</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.isArray(shipments) && shipments.map((shipment) => (
            <TableRow key={shipment.id}>
              <TableCell className="font-medium">
                <Button 
                  variant="link" 
                  className="h-auto p-0 text-primary font-medium"
                  onClick={() => {
                    setSelectedShipment(shipment);
                    setIsSheetOpen(true);
                  }}
                >
                  {shipment.tracking_number || shipment.external_reference_number || `SHIP-${shipment.id}`}
                </Button>
              </TableCell>
              <TableCell>{shipment.origin_address || "N/A"}</TableCell>
              <TableCell>{shipment.destination_address || "N/A"}</TableCell>
              <TableCell>
                <Badge className={`${getStatusColor(shipment.status)}`}>
                  {shipment.status}
                </Badge>
              </TableCell>
              <TableCell>{shipment.carrier?.full_name || shipment.carrier_id || "N/A"}</TableCell>
              <TableCell>
                {shipment.expected_delivery_date 
                  ? format(new Date(shipment.expected_delivery_date), "MMM dd, yyyy")
                  : "N/A"}
              </TableCell>
            </TableRow>
          ))}
          {(!Array.isArray(shipments) || shipments.length === 0) && (
            <TableRow>
              <TableCell colSpan={6} className="text-center py-6 text-muted-foreground">
                No shipments found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      {/* Sheet component for showing shipment details */}
      <ShipmentDetailSheet 
        shipment={selectedShipment} 
        isOpen={isSheetOpen} 
        onOpenChange={setIsSheetOpen} 
      />
    </div>
  );
}
