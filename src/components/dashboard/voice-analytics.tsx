"use client";

import { Card } from "@/components/ui/card";
import { Clock, Bot, Volume2 } from "lucide-react";
import { cn } from "@/lib/utils";

export function VoiceAnalytics() {
  // Mock voice analytics data - should come from actual voice API
  const voiceData = {
    totalCallsToday: 247,
    activeCalls: 12,
    avgCallDuration: 3.4,
    aiAssistanceRate: 85.2,
    peakHours: "10:00 AM - 2:00 PM",
    topIssues: [
      { issue: "Shipment Status Inquiry", count: 89, percentage: 36 },
      { issue: "Delivery Updates", count: 67, percentage: 27 },
      { issue: "Rate Quotes", count: 45, percentage: 18 },
      { issue: "Claims & Support", count: 46, percentage: 19 },
    ],
    recentCalls: [
      {
        id: "call-001",
        customer: "ABC Logistics",
        duration: "4:32",
        issue: "Shipment tracking",
        aiAssisted: true,
        resolved: true,
        time: "2 min ago",
      },
      {
        id: "call-002", 
        customer: "XYZ Freight",
        duration: "2:15",
        issue: "Rate inquiry",
        aiAssisted: true,
        resolved: true,
        time: "5 min ago",
      },
      {
        id: "call-003",
        customer: "Global Shipping Co",
        duration: "6:44",
        issue: "Delivery issue",
        aiAssisted: false,
        resolved: false,
        time: "8 min ago",
      },
    ],
  };

  return (
    <Card className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="flex items-center gap-2 mb-6">
        <Volume2 className="h-5 w-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-gray-900">Voice Analytics</h3>
      </div>

      <div className="space-y-6">
        {/* Quick Stats - BizLink Style */}
        <div className="grid grid-cols-1 gap-4">
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-600">Active Calls</span>
              <Volume2 className="h-4 w-4 text-blue-600" />
            </div>
            <div className="text-2xl font-semibold text-gray-900">{voiceData.activeCalls}</div>
            <div className="text-xs text-gray-500 mt-1">{voiceData.totalCallsToday} total today</div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-600">Avg Time</span>
                <Clock className="h-4 w-4 text-green-600" />
              </div>
              <div className="text-xl font-semibold text-gray-900">{voiceData.avgCallDuration}m</div>
            </div>

            <div className="p-4 bg-gray-50 rounded-lg border border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-600">AI Rate</span>
                <Bot className="h-4 w-4 text-purple-600" />
              </div>
              <div className="text-xl font-semibold text-gray-900">{voiceData.aiAssistanceRate}%</div>
            </div>
          </div>
        </div>

        {/* Call Issues Breakdown - BizLink Style */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-4">Top Call Topics</h4>
          <div className="space-y-3">
            {voiceData.topIssues.map((issue, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-100">
                <span className="text-sm font-medium text-gray-700">{issue.issue}</span>
                <div className="flex items-center gap-3">
                  <span className="text-sm font-semibold text-gray-900">{issue.count}</span>
                  <div className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden">
                    <div
                      className="h-full bg-blue-500 rounded-full"
                      style={{ width: `${issue.percentage}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Calls - BizLink Style */}
        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-4">Recent Calls</h4>
          <div className="space-y-3">
            {voiceData.recentCalls.map((call) => (
              <div key={call.id} className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-gray-900">{call.customer}</span>
                      {call.aiAssisted && (
                        <Bot className="h-3 w-3 text-purple-500" />
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      <span>{call.issue}</span>
                      <span>•</span>
                      <span>{call.duration}</span>
                      <span>•</span>
                      <span>{call.time}</span>
                    </div>
                  </div>
                  <div className={cn(
                    "px-2 py-1 rounded text-xs font-medium",
                    call.resolved
                      ? "bg-green-50 text-green-700 border border-green-200"
                      : "bg-yellow-50 text-yellow-700 border border-yellow-200"
                  )}>
                    {call.resolved ? "Resolved" : "In Progress"}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </Card>
  );
}