// src/components/layout/user-account-nav.tsx
"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LogOut, User as UserIcon, Settings, Shield, Clock } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { useAuth } from "@/contexts/AuthContext";

interface UserAccountNavProps {
  user?: {
    name?: string;
    email: string;
    image?: string;
  };
}

export function UserAccountNav({ user }: UserAccountNavProps) {
  const context = useOrganizationContext();
  const { logout: authLogout, user: authUser, canAccessWithoutPIN } = useAuth();

  // Handles both organization context logout and user authentication logout
  const logout = () => {
    // Clear organization context
    if (context) {
      context.setOrganizationContext(null);
    }
    
    // Call auth logout (which also redirects to landing page)
    authLogout();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger className="focus:outline-none">
        <Avatar className="h-8 w-8 border border-slate-100 ring-2 ring-blue-50">
          <AvatarImage src={user?.image} alt={user?.name || ""} />
          <AvatarFallback className="bg-gradient-to-r from-blue-100 to-teal-100 text-blue-600 font-medium">
            {user?.name?.[0]?.toUpperCase() || user?.email?.[0]?.toUpperCase() || "U"}
          </AvatarFallback>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-64 rounded-xl border-slate-200 shadow-md">
        <div className="p-4 bg-gradient-to-r from-blue-50/30 to-teal-50/30 rounded-t-xl">
          {user?.name && <p className="font-medium text-slate-800">{user.name}</p>}
          {user?.email && (
            <p className="text-xs text-muted-foreground truncate">{user.email}</p>
          )}
          
          {/* Role and access status */}
          {authUser && (
            <div className="flex flex-col gap-2 mt-2">
              <Badge variant="outline" className="text-xs w-fit rounded-lg border-slate-200">
                <Shield className="w-3 h-3 mr-1 text-blue-600" />
                <span className="bg-gradient-to-r from-blue-700 to-teal-600 bg-clip-text text-transparent font-medium">
                  {authUser.role}
                </span>
              </Badge>
              
              <div className="text-xs">
                {canAccessWithoutPIN("general") ? (
                  <span className="flex items-center">
                    <Clock className="w-3 h-3 mr-1 text-green-600" />
                    <span className="bg-gradient-to-r from-green-600 to-teal-600 bg-clip-text text-transparent font-medium">
                      Instant Access Active
                    </span>
                  </span>
                ) : (
                  <span className="flex items-center">
                    <Shield className="w-3 h-3 mr-1 text-amber-600" />
                    <span className="bg-gradient-to-r from-amber-600 to-amber-700 bg-clip-text text-transparent font-medium">
                      PIN Authentication Required
                    </span>
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
        <DropdownMenuSeparator />
        <DropdownMenuItem asChild>
          <a href="/dashboard/profile" className="flex items-center cursor-pointer rounded-lg hover:bg-blue-50/50 focus:bg-blue-50/50">
            <UserIcon className="mr-2 h-4 w-4 text-blue-600" />
            Profile
          </a>
        </DropdownMenuItem>
        <DropdownMenuItem asChild>
          <a href="/dashboard/settings" className="flex items-center cursor-pointer rounded-lg hover:bg-blue-50/50 focus:bg-blue-50/50">
            <Settings className="mr-2 h-4 w-4 text-blue-600" />
            Settings
          </a>
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          className="cursor-pointer text-destructive focus:text-destructive rounded-lg hover:bg-red-50/50 focus:bg-red-50/50" 
          onClick={logout}
        >
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
