"use client";

import { useState } from "react";
import { useOrganizationContext } from "@/contexts/OrganizationContext";
import { Building, ChevronDown, LogOut } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Dialog, 
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { OrganizationSelector } from "@/components/organization/OrganizationSelector";

export function OrganizationSwitcher() {
  const [open, setOpen] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const context = useOrganizationContext();

  if (!context || !context.organizationId) return null;

  const { organizationId, setOrganizationContext, organizations } = context;

  // Function to handle switching organization
  const handleSwitchOrganization = () => {
    setDialogOpen(true);
    setOpen(false);
  };

  // Function to handle logging out of organization
  const handleLogout = () => {
    setOrganizationContext(null);
    setOpen(false);
  };

  // Find current organization name from organizations array or localStorage
  const currentOrg = organizations.find(org => org.id.toString() === organizationId);
  const storedOrgName = typeof window !== 'undefined' ? localStorage.getItem('organizationName') : null;
  
  // Use organization name in this priority: 
  // 1. From organizations array if found
  // 2. From localStorage if stored
  // 3. Fall back to ID if available
  // 4. Generic default name
  const orgName = currentOrg?.name || 
                 storedOrgName || 
                 (organizationId ? `Organization ${organizationId}` : "Current Organization");

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="min-w-[180px] justify-start gap-2 rounded-xl border-slate-200 hover:border-blue-200 hover:bg-blue-50/50"
          >
            <Building className="h-4 w-4 text-blue-600" />
            <span className="truncate font-medium">{orgName}</span>
            <ChevronDown className="ml-auto h-4 w-4 text-blue-400" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="start" className="w-[240px] rounded-xl border-slate-200 shadow-md">
          <DropdownMenuLabel className="bg-gradient-to-r from-blue-50/40 to-teal-50/40">
            <span className="bg-gradient-to-r from-blue-700 to-teal-600 bg-clip-text text-transparent font-medium">Organization</span>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          <div className="px-2 py-1.5">
            <Badge variant="outline" className="w-full justify-start font-normal px-2 py-1 rounded-lg border-slate-200">
              <span className="text-muted-foreground">ID: </span>
              <span className="text-slate-700">{organizationId}</span>
            </Badge>
          </div>
          <DropdownMenuSeparator />
          <DropdownMenuItem 
            onClick={handleSwitchOrganization}
            className="rounded-lg hover:bg-blue-50/50 focus:bg-blue-50/50"
          >
            <Building className="mr-2 h-4 w-4 text-blue-600" />
            Switch Organization
          </DropdownMenuItem>
          <DropdownMenuItem 
            onClick={handleLogout} 
            className="text-destructive focus:text-destructive rounded-lg hover:bg-red-50/50 focus:bg-red-50/50"
          >
            <LogOut className="mr-2 h-4 w-4" />
            Log Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent className="sm:max-w-md rounded-2xl">
          <DialogHeader>
            <DialogTitle className="bg-gradient-to-r from-blue-700 to-teal-600 bg-clip-text text-transparent font-bold">Switch Organization</DialogTitle>
            <DialogDescription>
              Select a different organization to work with
            </DialogDescription>
          </DialogHeader>
          <OrganizationSelector />
          <DialogFooter className="sm:justify-start">
            <Button 
              type="button" 
              variant="secondary" 
              onClick={() => setDialogOpen(false)}
              className="rounded-xl hover:bg-blue-50/50"
            >
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
