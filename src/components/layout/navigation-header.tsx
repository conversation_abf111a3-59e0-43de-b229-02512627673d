// src/components/layout/navigation-header.tsx
"use client";

import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/AuthContext";

export function NavigationHeader() {
  const router = useRouter();
  const { isAuthenticated } = useAuth();

  return (
    <header className="fixed top-2 sm:top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-4xl px-2 sm:px-4">
      <div className="bg-white/90 backdrop-blur-xl border border-white/20 shadow-lg rounded-xl px-3 sm:px-6 py-2 sm:py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 sm:gap-3">
            <Link href="/" className="flex items-center gap-2 sm:gap-3">
              <Image
                src="/assets/logo.png"
                alt="PACE Logo"
                width={32}
                height={32}
                className="h-6 w-6 sm:h-8 sm:w-8 rounded"
              />
              <div className="flex items-center">
                <h1 className="text-lg sm:text-xl font-manrope font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                  PACE
                </h1>
              </div>
            </Link>
          </div>
          
          <div className="flex items-center gap-2 sm:gap-6">
            <Link 
              href="/#features" 
              className="hidden sm:block text-sm font-inter font-medium text-gray-600 hover:text-blue-600 transition-colors"
            >
              Features
            </Link>
            <Link 
              href="/#about" 
              className="hidden sm:block text-sm font-inter font-medium text-gray-600 hover:text-blue-600 transition-colors"
            >
              About
            </Link>
            <Link 
              href="/privacy" 
              className="hidden md:block text-sm font-inter font-medium text-gray-600 hover:text-blue-600 transition-colors"
            >
              Privacy
            </Link>
            <Link 
              href="/terms" 
              className="hidden md:block text-sm font-inter font-medium text-gray-600 hover:text-blue-600 transition-colors"
            >
              Terms
            </Link>
            <Link 
              href="/contact" 
              className="hidden md:block text-sm font-inter font-medium text-gray-600 hover:text-blue-600 transition-colors"
            >
              Contact
            </Link>
            <Link 
              href="/support" 
              className="hidden md:block text-sm font-inter font-medium text-gray-600 hover:text-blue-600 transition-colors"
            >
              Support
            </Link>
            <Button 
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-inter font-medium px-3 sm:px-6 py-1.5 sm:py-2 text-sm rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 active:scale-95 touch-manipulation min-h-[44px] min-w-[44px]"
              role="button"
              aria-label="Get started with PACE"
              onClick={() => {
                if (isAuthenticated) {
                  router.push("/dashboard");
                } else {
                  router.push("/login");
                }
              }}
            >
              <span className="hidden sm:inline">Get Started</span>
              <span className="sm:hidden">Login</span>
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
}
