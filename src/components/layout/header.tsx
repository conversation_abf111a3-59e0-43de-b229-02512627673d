// src/components/layout/header.tsx
"use client";

import { useState } from "react";
import { UserAccountNav } from "./user-account-nav";
import { OrganizationSwitcher } from "./organization-switcher";
import { <PERSON>, <PERSON>u, Shield, Clock } from "lucide-react";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Sidebar } from "./sidebar";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { trackFeatureUsage } from "@/lib/analytics";

interface HeaderProps {
  user?: {
    name?: string;
    email: string;
    image?: string;
  };
}

export function Header({ user }: HeaderProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { user: authUser } = useAuth();

  // Use authenticated user for display, fallback to legacy user format
  const displayUser = authUser ? {
    name: authUser.full_name,
    email: authUser.email,
    image: undefined
  } : user;

  return (
    <header className="sticky top-0 z-30 w-full">
      <div className="glass-card bg-white/80 dark:bg-slate-900/80 backdrop-blur-xl rounded-2xl border border-white/20 dark:border-slate-700/50 shadow-xl mx-4 my-2">
        <div className="flex h-16 items-center px-4 md:px-6">
          <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" className="mr-2 px-0 text-base hover:bg-white/50 dark:hover:bg-slate-800/50 focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden rounded-xl">
                <Menu className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                <span className="sr-only">Toggle sidebar</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0">
              <Sidebar />
            </SheetContent>
          </Sheet>
          <div className="w-full flex justify-between items-center">
          {/* Mobile: Show PACE + Current Organization */}
          <div className="flex items-center gap-3 md:hidden">
            <div className="font-manrope font-bold text-lg bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">PACE</div>
            {authUser && (
              <div className="flex items-center gap-2">
                <div className="h-1 w-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full"></div>
                <Badge className="text-xs px-3 py-1 bg-gradient-to-r from-blue-500/10 to-teal-500/10 border border-blue-200/50 dark:border-blue-800/50 rounded-full">
                  <span className="text-blue-700 dark:text-blue-300 font-medium truncate max-w-[80px]">{authUser.role}</span>
                </Badge>
              </div>
            )}
          </div>

          {/* Desktop: Full context */}
          <div className="hidden md:flex items-center gap-4">
            <OrganizationSwitcher />

            {/* Enhanced Role and access indicators */}
            {authUser && (
              <div className="flex items-center gap-3">
                <Badge className="text-xs px-3 py-1.5 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-200/50 dark:border-blue-800/50 rounded-full">
                  <Shield className="w-3 h-3 mr-1.5 text-blue-600 dark:text-blue-400" />
                  <span className="text-blue-700 dark:text-blue-300 font-medium">{authUser.role}</span>
                </Badge>

                <Badge className="text-xs px-3 py-1.5 bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-200/50 dark:border-green-800/50 rounded-full">
                  <Clock className="w-3 h-3 mr-1.5 text-green-600 dark:text-green-400" />
                  <span className="text-green-700 dark:text-green-300 font-medium">Authenticated</span>
                </Badge>
              </div>
            )}
          </div>
          <div className="ml-auto flex items-center space-x-3 md:space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="relative hover:bg-white/50 dark:hover:bg-slate-800/50 transition-all duration-300 h-10 w-10 rounded-xl group"
              onClick={() => trackFeatureUsage('Notification Bell Clicked')}
            >
              <Bell className="h-5 w-5 text-slate-600 dark:text-slate-400 group-hover:text-slate-800 dark:group-hover:text-slate-200 transition-colors" />
              {/* Notification indicator */}
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-red-500 to-orange-500 rounded-full border-2 border-white dark:border-slate-900 animate-pulse"></div>
              <span className="sr-only">Notifications</span>
            </Button>
            {displayUser && <UserAccountNav user={displayUser} />}
          </div>
          </div>
        </div>
      </div>
    </header>
  );
}
