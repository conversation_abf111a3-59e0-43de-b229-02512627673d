// src/components/layout/header.tsx
"use client";

import { useState } from "react";
import { UserAccountNav } from "./user-account-nav";
import { OrganizationSwitcher } from "./organization-switcher";
import { <PERSON>, <PERSON>u, Shield, Clock } from "lucide-react";
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet";
import { Sidebar } from "./sidebar";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { useAuth } from "@/contexts/AuthContext";
import { trackFeatureUsage } from "@/lib/analytics";

interface HeaderProps {
  user?: {
    name?: string;
    email: string;
    image?: string;
  };
}

export function Header({ user }: HeaderProps) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { user: authUser } = useAuth();

  // Use authenticated user for display, fallback to legacy user format
  const displayUser = authUser ? {
    name: authUser.full_name,
    email: authUser.email,
    image: undefined
  } : user;

  return (
    <header className="sticky top-0 z-30 w-full">
      <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700">
        <div className="flex h-16 items-center px-4 md:px-6">
          <Sheet open={isSidebarOpen} onOpenChange={setIsSidebarOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" className="mr-2 px-0 text-base hover:bg-slate-100 dark:hover:bg-slate-700 focus-visible:bg-transparent focus-visible:ring-0 focus-visible:ring-offset-0 md:hidden">
                <Menu className="h-5 w-5 text-slate-600 dark:text-slate-400" />
                <span className="sr-only">Toggle sidebar</span>
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="p-0">
              <Sidebar />
            </SheetContent>
          </Sheet>
          <div className="w-full flex justify-between items-center">
          {/* Mobile: Show PACE + Current Organization */}
          <div className="flex items-center gap-2 md:hidden">
            <div className="font-manrope font-bold text-lg text-slate-900 dark:text-slate-100">PACE</div>
            {authUser && (
              <div className="flex items-center gap-1">
                <div className="h-1 w-1 bg-slate-400 rounded-full"></div>
                <Badge variant="outline" className="text-xs px-2 py-0.5 border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800">
                  <span className="text-slate-700 dark:text-slate-300 font-medium truncate max-w-[80px]">{authUser.role}</span>
                </Badge>
              </div>
            )}
          </div>

          {/* Desktop: Full context */}
          <div className="hidden md:flex items-center gap-3">
            <OrganizationSwitcher />

            {/* Role and access indicator */}
            {authUser && (
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800">
                  <Shield className="w-3 h-3 mr-1 text-slate-600 dark:text-slate-400" />
                  <span className="text-slate-700 dark:text-slate-300 font-medium">{authUser.role}</span>
                </Badge>

                <Badge variant="secondary" className="text-xs border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-900/20">
                  <Clock className="w-3 h-3 mr-1 text-green-600 dark:text-green-400" />
                  <span className="text-green-700 dark:text-green-300 font-medium">Authenticated</span>
                </Badge>
              </div>
            )}
          </div>
          <div className="ml-auto flex items-center space-x-2 md:space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors duration-200 h-10 w-10"
              onClick={() => trackFeatureUsage('Notification Bell Clicked')}
            >
              <Bell className="h-5 w-5 text-slate-600 dark:text-slate-400" />
              <span className="sr-only">Notifications</span>
            </Button>
            {displayUser && <UserAccountNav user={displayUser} />}
          </div>
          </div>
        </div>
      </div>
    </header>
  );
}
