"use client";

import { useEffect, useState } from 'react';
import DottedMap from 'dotted-map';

export default function WorldMap({ className = "" }) {
  const [svgContent, setSvgContent] = useState('');
  
  useEffect(() => {
    try {
      // Create the map with diagonal grid pattern
      const map = new DottedMap({ 
        height: 85,  
        grid: 'diagonal'
      });
  
      // Add some key global shipping/logistics hubs as pins with blue-green colors
      const pins = [
        // North America
        { lat: 40.73061, lng: -73.935242, color: '#34d399' }, // New York
        { lat: 37.7749, lng: -122.4194, color: '#3b82f6' },   // San Francisco
        { lat: 25.7617, lng: -80.1918, color: '#10b981' },    // Miami
        { lat: 29.7604, lng: -95.3698, color: '#2563eb' },    // Houston
        { lat: 41.8781, lng: -87.6298, color: '#34d399' },    // Chicago
        { lat: 49.2827, lng: -123.1207, color: '#3b82f6' },   // Vancouver
        { lat: 45.5017, lng: -73.5673, color: '#10b981' },    // Montreal
        
        // Europe
        { lat: 51.5074, lng: -0.1278, color: '#3b82f6' },     // London
        { lat: 52.3676, lng: 4.9041, color: '#34d399' },      // Amsterdam
        { lat: 50.8503, lng: 4.3517, color: '#10b981' },      // Brussels
        { lat: 48.8566, lng: 2.3522, color: '#2563eb' },      // Paris
        { lat: 41.3851, lng: 2.1734, color: '#34d399' },      // Barcelona
        { lat: 55.6761, lng: 12.5683, color: '#3b82f6' },     // Copenhagen
        { lat: 53.5511, lng: 9.9937, color: '#10b981' },      // Hamburg
        
        // Asia
        { lat: 31.2304, lng: 121.4737, color: '#34d399' },    // Shanghai
        { lat: 22.3193, lng: 114.1694, color: '#3b82f6' },    // Hong Kong
        { lat: 35.6762, lng: 139.6503, color: '#10b981' },    // Tokyo
        { lat: 1.3521, lng: 103.8198, color: '#2563eb' },     // Singapore
        { lat: 25.2048, lng: 55.2708, color: '#34d399' },     // Dubai
        { lat: 19.0760, lng: 72.8777, color: '#3b82f6' },     // Mumbai
        
        // Australia & Oceania
        { lat: -33.8688, lng: 151.2093, color: '#10b981' },   // Sydney
        { lat: -37.8136, lng: 144.9631, color: '#3b82f6' },   // Melbourne
        { lat: -41.2865, lng: 174.7762, color: '#34d399' },   // Wellington
        
        // South America
        { lat: -22.9068, lng: -43.1729, color: '#2563eb' },   // Rio de Janeiro
        { lat: -34.6037, lng: -58.3816, color: '#10b981' },   // Buenos Aires
        { lat: -33.4489, lng: -70.6693, color: '#3b82f6' },   // Santiago
        
        // Africa
        { lat: 33.9716, lng: -6.8498, color: '#34d399' },     // Rabat
        { lat: 30.0444, lng: 31.2357, color: '#10b981' },     // Cairo
        { lat: -33.9249, lng: 18.4241, color: '#2563eb' },    // Cape Town
      ];
      
      // Add pins to the map
      pins.forEach(pin => {
        map.addPin({
          lat: pin.lat,
          lng: pin.lng,
          svgOptions: { 
            color: pin.color,
            radius: 0.7, // Slightly larger pins
          }
        });
      });
  
      // Generate the SVG with blue-green dots on a black background
      const svgMap = map.getSVG({
        radius: 0.23, // Slightly larger dots
        color: '#0d9488', // Base teal color for dots
        shape: 'circle',
        backgroundColor: 'transparent',
      });
  
      setSvgContent(svgMap);
    } catch (error) {
      console.error("Error generating dotted map:", error);
      // Fallback to empty content
      setSvgContent('');
    }
  }, []);

  return (
    <div className={`${className} w-full relative opacity-80 hover:opacity-100 transition-opacity duration-1000`}>
      <div 
        className="w-full h-full" 
        dangerouslySetInnerHTML={{ __html: svgContent }}
      />
    </div>
  );
}
