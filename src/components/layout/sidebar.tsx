// src/components/layout/sidebar.tsx
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { 
  Home,
  Package,
  MapPin,
  Users,
  Building,
  Settings,
  BarChart3,
  Mic,
  Receipt,
  UserCircle,
  LogOut,
  FileText
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import { UserRole } from "@/lib/types";

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles?: UserRole[]; // Roles that can access this item
  requiresPIN?: boolean; // Whether this item requires PIN authentication
}

interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

const navigationSections: NavigationSection[] = [
  {
    title: "OPERATIONS",
    items: [
      { 
        name: "Dashboard", 
        href: "/dashboard", 
        icon: Home 
      },
      { 
        name: "Shipments", 
        href: "/dashboard/shipments", 
        icon: Package
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Tracking", 
        href: "/dashboard/tracking", 
        icon: MapPin
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Quotes", 
        href: "/dashboard/quotes", 
        icon: FileText
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Voice Intelligence", 
        href: "/dashboard/voice", 
        icon: Mic
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Reports", 
        href: "/dashboard/reports", 
        icon: BarChart3,
        roles: ["ADMIN", "BROKER", "CARRIER"]
      },
    ]
  },
  {
    title: "MANAGEMENT",
    items: [
      { 
        name: "Users", 
        href: "/dashboard/users", 
        icon: Users,
        roles: ["ADMIN", "BROKER"]
      },
      { 
        name: "Organizations", 
        href: "/dashboard/organizations", 
        icon: Building,
        roles: ["ADMIN", "BROKER"]
      },
      { 
        name: "Analytics", 
        href: "/dashboard/analytics", 
        icon: Receipt,
        roles: ["ADMIN", "BROKER"],
        requiresPIN: true
      },
    ]
  },
  {
    title: "SYSTEM",
    items: [
      { 
        name: "Settings", 
        href: "/dashboard/settings", 
        icon: Settings,
        roles: ["ADMIN"],
        requiresPIN: true
      },
    ]
  }
];

export function Sidebar() {
  const pathname = usePathname();
  const { user, hasRole, canAccessWithoutPIN } = useAuth();

  // Filter navigation items based on user role and PIN requirements
  const filterItems = (items: NavigationItem[]) => {
    return items.filter(item => {
      // Always show items without role restrictions
      if (!item.roles) return true;
      
      // Check if user has required role
      if (!item.roles.some(role => hasRole(role))) return false;
      
      // For items requiring PIN, check if user can access without PIN
      if (item.requiresPIN && !canAccessWithoutPIN("navigation")) {
        // For now, show but could implement special handling
        return true;
      }
      
      return true;
    });
  };

  return (
    <div className="flex h-full flex-col glass-card bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl border-r border-white/20 dark:border-slate-700/50">
      {/* Enhanced Header */}
      <div className="flex flex-shrink-0 items-center h-16 px-6 border-b border-white/20 dark:border-slate-700/50">
        <Link href="/dashboard" className="flex items-center gap-3 group">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-teal-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-110">
            <span className="text-white font-bold text-lg">P</span>
          </div>
          <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-teal-500 bg-clip-text text-transparent">
            PACE
          </span>
        </Link>
      </div>
      
      {/* Enhanced Navigation */}
      <nav className="flex-1 px-4 py-6 overflow-y-auto">
        {navigationSections.map((section) => {
          const filteredItems = filterItems(section.items);
          if (filteredItems.length === 0) return null;

          return (
            <div key={section.title} className="mb-8">
              <h3 className="text-xs font-bold text-slate-500 dark:text-slate-400 uppercase tracking-wider mb-4 px-3 flex items-center gap-2">
                <div className="w-4 h-px bg-gradient-to-r from-blue-500 to-teal-500"></div>
                {section.title}
              </h3>
              <div className="space-y-2">
                {filteredItems.map((item) => {
                  const isActive = pathname === item.href || pathname?.startsWith(`${item.href}/`);

                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        isActive
                          ? "bg-gradient-to-r from-blue-500/10 to-teal-500/10 text-blue-600 dark:text-blue-400 border-r-2 border-blue-500"
                          : "text-slate-600 dark:text-slate-400 hover:bg-white/50 dark:hover:bg-slate-800/50 hover:text-slate-900 dark:hover:text-slate-200",
                        "group flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-300 hover:scale-[1.02] hover:shadow-md relative overflow-hidden"
                      )}
                    >
                      {/* Active indicator */}
                      {isActive && (
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-teal-500/5 rounded-xl" />
                      )}

                      <div className={cn(
                        "p-2 rounded-lg mr-3 transition-all duration-300",
                        isActive
                          ? "bg-gradient-to-r from-blue-500 to-teal-500 shadow-lg shadow-blue-500/25"
                          : "bg-slate-100 dark:bg-slate-700 group-hover:bg-slate-200 dark:group-hover:bg-slate-600"
                      )}>
                        <item.icon
                          className={cn(
                            isActive
                              ? "text-white"
                              : "text-slate-500 dark:text-slate-400 group-hover:text-slate-700 dark:group-hover:text-slate-300",
                            "h-4 w-4 flex-shrink-0"
                          )}
                          aria-hidden="true"
                        />
                      </div>

                      <span className="relative z-10 flex-1">{item.name}</span>

                      {item.requiresPIN && (
                        <div className="ml-auto w-2 h-2 bg-gradient-to-r from-amber-400 to-orange-500 rounded-full shadow-sm"
                             title="Requires PIN authentication" />
                      )}
                    </Link>
                  );
                })}
              </div>
            </div>
          );
        })}
      </nav>
      
      {/* Enhanced User Profile Section */}
      <div className="border-t border-white/20 dark:border-slate-700/50 p-4">
        <div className="glass-card bg-white/50 dark:bg-slate-800/50 rounded-2xl p-4 mb-3">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-teal-500 rounded-xl flex items-center justify-center shadow-lg">
              <UserCircle className="h-6 w-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold text-slate-900 dark:text-slate-100 truncate">
                {user?.email?.split('@')[0] || user?.full_name || "User"}
              </p>
              <p className="text-xs text-slate-500 dark:text-slate-400 capitalize flex items-center gap-1">
                <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                {user?.role?.toLowerCase() || "Freight Manager"}
              </p>
            </div>
          </div>
        </div>
        <button className="w-full flex items-center gap-3 px-4 py-3 text-sm text-slate-600 dark:text-slate-400 hover:bg-white/50 dark:hover:bg-slate-800/50 rounded-xl transition-all duration-300 hover:scale-[1.02] group">
          <div className="p-1.5 bg-slate-100 dark:bg-slate-700 rounded-lg group-hover:bg-red-100 dark:group-hover:bg-red-900/30 transition-colors">
            <LogOut className="h-4 w-4 group-hover:text-red-600 dark:group-hover:text-red-400" />
          </div>
          <span className="group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors">Log out</span>
        </button>
      </div>
    </div>
  );
}
