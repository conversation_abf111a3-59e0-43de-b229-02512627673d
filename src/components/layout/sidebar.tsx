// src/components/layout/sidebar.tsx
"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { 
  Home,
  Package,
  MapPin,
  Users,
  Building,
  Settings,
  BarChart3,
  Mic,
  Receipt,
  UserCircle,
  LogOut,
  FileText
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/AuthContext";
import { UserRole } from "@/lib/types";

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  roles?: UserRole[]; // Roles that can access this item
  requiresPIN?: boolean; // Whether this item requires PIN authentication
}

interface NavigationSection {
  title: string;
  items: NavigationItem[];
}

const navigationSections: NavigationSection[] = [
  {
    title: "OPERATIONS",
    items: [
      { 
        name: "Dashboard", 
        href: "/dashboard", 
        icon: Home 
      },
      { 
        name: "Shipments", 
        href: "/dashboard/shipments", 
        icon: Package
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Tracking", 
        href: "/dashboard/tracking", 
        icon: MapPin
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Quotes", 
        href: "/dashboard/quotes", 
        icon: FileText
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Voice Intelligence", 
        href: "/dashboard/voice", 
        icon: Mic
        // Removed role restrictions to make it always visible
      },
      { 
        name: "Reports", 
        href: "/dashboard/reports", 
        icon: BarChart3,
        roles: ["ADMIN", "BROKER", "CARRIER"]
      },
    ]
  },
  {
    title: "MANAGEMENT",
    items: [
      { 
        name: "Users", 
        href: "/dashboard/users", 
        icon: Users,
        roles: ["ADMIN", "BROKER"]
      },
      { 
        name: "Organizations", 
        href: "/dashboard/organizations", 
        icon: Building,
        roles: ["ADMIN", "BROKER"]
      },
      { 
        name: "Analytics", 
        href: "/dashboard/analytics", 
        icon: Receipt,
        roles: ["ADMIN", "BROKER"],
        requiresPIN: true
      },
    ]
  },
  {
    title: "SYSTEM",
    items: [
      { 
        name: "Settings", 
        href: "/dashboard/settings", 
        icon: Settings,
        roles: ["ADMIN"],
        requiresPIN: true
      },
    ]
  }
];

export function Sidebar() {
  const pathname = usePathname();
  const { user, hasRole, canAccessWithoutPIN } = useAuth();

  // Filter navigation items based on user role and PIN requirements
  const filterItems = (items: NavigationItem[]) => {
    return items.filter(item => {
      // Always show items without role restrictions
      if (!item.roles) return true;
      
      // Check if user has required role
      if (!item.roles.some(role => hasRole(role))) return false;
      
      // For items requiring PIN, check if user can access without PIN
      if (item.requiresPIN && !canAccessWithoutPIN("navigation")) {
        // For now, show but could implement special handling
        return true;
      }
      
      return true;
    });
  };

  return (
    <div className="flex h-full flex-col bg-white border-r border-gray-200">
      {/* Header */}
      <div className="flex flex-shrink-0 items-center h-16 px-6 border-b border-gray-200">
        <Link href="/dashboard" className="flex items-center gap-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-teal-500 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">P</span>
          </div>
          <span className="text-xl font-bold text-gray-900">PACE</span>
        </Link>
      </div>
      
      {/* Navigation */}
      <nav className="flex-1 px-4 py-6 overflow-y-auto">
        {navigationSections.map((section) => {
          const filteredItems = filterItems(section.items);
          if (filteredItems.length === 0) return null;
          
          return (
            <div key={section.title} className="mb-8">
              <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-2">
                {section.title}
              </h3>
              <div className="space-y-1">
                {filteredItems.map((item) => {
                  const isActive = pathname === item.href || pathname?.startsWith(`${item.href}/`);
                  
                  return (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        isActive
                          ? "bg-gray-100 text-gray-900"
                          : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                        "group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors duration-200"
                      )}
                    >
                      <item.icon
                        className={cn(
                          isActive 
                            ? "text-gray-900" 
                            : "text-gray-400 group-hover:text-gray-600",
                          "mr-3 h-5 w-5 flex-shrink-0"
                        )}
                        aria-hidden="true"
                      />
                      <span>{item.name}</span>
                      {item.requiresPIN && (
                        <div className="ml-auto w-2 h-2 bg-amber-500 rounded-full" 
                             title="Requires PIN authentication" />
                      )}
                    </Link>
                  );
                })}
              </div>
            </div>
          );
        })}
      </nav>
      
      {/* User Profile Section */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            <UserCircle className="h-5 w-5 text-gray-600" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user?.email?.split('@')[0] || user?.full_name || "User"}
            </p>
            <p className="text-xs text-gray-500 capitalize">
              {user?.role?.toLowerCase() || "Freight Manager"}
            </p>
          </div>
        </div>
        <button className="w-full flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
          <LogOut className="h-4 w-4" />
          Log out
        </button>
      </div>
    </div>
  );
}
