"use client";

import { useEffect } from 'react';
import Script from 'next/script';

export function WaitlistWidget() {
  useEffect(() => {
    // Ensure the widget is properly initialized after the script loads
    return () => {
      // Cleanup if needed
    };
  }, []);

  return (
    <>
      <div 
        id="getWaitlistContainer" 
        data-waitlist_id="29081" 
        data-widget_type="WIDGET_1"
        className="w-full max-w-md mx-auto"
      />
      <link 
        rel="stylesheet" 
        type="text/css" 
        href="https://prod-waitlist-widget.s3.us-east-2.amazonaws.com/getwaitlist.min.css"
      />
      <Script
        src="https://prod-waitlist-widget.s3.us-east-2.amazonaws.com/getwaitlist.min.js"
        strategy="afterInteractive"
      />
    </>
  );
}
