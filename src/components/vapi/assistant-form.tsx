"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { 
  Bo<PERSON>, 
  Brain, 
  Volume2, 
  Code, 
  Trash2, 
  Save,
  X
} from "lucide-react";
import { VAPIAssistant, VAPIFunction } from "@/lib/types";

interface AssistantFormProps {
  assistant?: VAPIAssistant;
  onSave: (assistant: Partial<VAPIAssistant>) => void;
  onCancel: () => void;
}

export function AssistantForm({ assistant, onSave, onCancel }: AssistantFormProps) {
  const availableModels = {
    openai: ["gpt-4", "gpt-4-turbo", "gpt-3.5-turbo"],
    anthropic: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"]
  };

  const availableVoices = {
    openai: ["alloy", "echo", "fable", "onyx", "nova", "shimmer"],
    elevenlabs: ["21m00Tcm4TlvDq8ikWAM", "AZnzlk1XvdvUeBnXmlld", "EXAVITQu4vr4xnSDxMaL"]
  };

  const defaultFunctions: VAPIFunction[] = [
    {
      name: "get_shipment_status",
      description: "Get real-time shipment tracking information and status updates",
      parameters: {
        type: "object",
        properties: {
          tracking_number: { 
            type: "string", 
            description: "The tracking number of the shipment" 
          }
        },
        required: ["tracking_number"]
      }
    },
    {
      name: "create_shipment_alert",
      description: "Create alerts for shipment delays or issues",
      parameters: {
        type: "object",
        properties: {
          tracking_number: { 
            type: "string", 
            description: "The tracking number of the shipment" 
          },
          alert_type: { 
            type: "string", 
            enum: ["delay", "damage", "customs_hold", "delivery_attempt"], 
            description: "Type of alert to create" 
          },
          message: { 
            type: "string", 
            description: "Custom alert message" 
          }
        },
        required: ["tracking_number", "alert_type"]
      }
    },
    {
      name: "notify_stakeholders",
      description: "Send notifications to relevant stakeholders about shipment updates",
      parameters: {
        type: "object",
        properties: {
          tracking_number: { 
            type: "string", 
            description: "The tracking number of the shipment" 
          },
          stakeholder_types: { 
            type: "array", 
            items: { type: "string" },
            description: "Types of stakeholders to notify (consignee, warehouse, customer_service, etc.)" 
          },
          message: { 
            type: "string", 
            description: "Notification message" 
          }
        },
        required: ["tracking_number", "stakeholder_types", "message"]
      }
    }
  ];

  const [formData, setFormData] = useState<Partial<VAPIAssistant>>({
    name: assistant?.name || "",
    description: assistant?.description || "",
    model: {
      provider: assistant?.model.provider || "openai",
      model: assistant?.model.model || "gpt-4",
      temperature: assistant?.model.temperature || 0.7,
      // Auto-add default functions for new assistants, keep existing for edits
      functions: assistant?.model.functions || defaultFunctions
    },
    voice: {
      provider: assistant?.voice.provider || "openai",
      voiceId: assistant?.voice.voiceId || "alloy"
    },
    serverUrl: assistant?.serverUrl || (
      process.env.NODE_ENV === 'development' 
        ? "http://localhost:3001/api/vapi-tools" 
        : "https://your-domain.com/api/vapi-tools"
    ),
    is_active: assistant?.is_active ?? true
  });

  const addFunction = (functionToAdd: VAPIFunction) => {
    const currentFunctions = formData.model?.functions || [];
    if (!currentFunctions.find(f => f.name === functionToAdd.name)) {
      setFormData({
        ...formData,
        model: {
          ...formData.model!,
          functions: [...currentFunctions, functionToAdd]
        }
      });
    }
  };

  const removeFunction = (functionName: string) => {
    const currentFunctions = formData.model?.functions || [];
    setFormData({
      ...formData,
      model: {
        ...formData.model!,
        functions: currentFunctions.filter(f => f.name !== functionName)
      }
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <Card className="w-full max-w-4xl mx-auto bg-white/90 backdrop-blur-sm border-blue-100 shadow-xl">
      <CardHeader>
        <CardTitle className="font-manrope bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center gap-2">
          <Bot className="h-6 w-6 text-blue-600" />
          {assistant ? "Edit Assistant" : "Create New Assistant"}
        </CardTitle>
        <CardDescription className="font-inter text-gray-600">
          Configure your VAPI assistant with AI model, voice settings, and available functions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="name" className="font-inter font-medium">Assistant Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="e.g., PACE Freight Assistant"
                required
              />
            </div>
            <div className="space-y-2">
              <Label className="font-inter font-medium">Status</Label>
              <Select 
                value={formData.is_active ? "active" : "inactive"}
                onValueChange={(value) => setFormData({...formData, is_active: value === "active"})}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="font-inter font-medium">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              placeholder="Describe what this assistant does..."
              rows={2}
            />
          </div>

          {/* AI Model Configuration */}
          <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-100">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-manrope flex items-center gap-2 text-blue-700">
                <Brain className="h-5 w-5" />
                AI Model Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="space-y-2">
                  <Label className="font-inter font-medium">Provider</Label>
                  <Select 
                    value={formData.model?.provider}
                    onValueChange={(value: "openai" | "anthropic") => setFormData({
                      ...formData,
                      model: { ...formData.model!, provider: value, model: availableModels[value][0] }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI</SelectItem>
                      <SelectItem value="anthropic">Anthropic</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="font-inter font-medium">Model</Label>
                  <Select 
                    value={formData.model?.model}
                    onValueChange={(value) => setFormData({
                      ...formData,
                      model: { ...formData.model!, model: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {availableModels[formData.model?.provider || "openai"].map((model) => (
                        <SelectItem key={model} value={model}>{model}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="temperature" className="font-inter font-medium">Temperature</Label>
                  <Input
                    id="temperature"
                    type="number"
                    min="0"
                    max="2"
                    step="0.1"
                    value={formData.model?.temperature}
                    onChange={(e) => setFormData({
                      ...formData,
                      model: { ...formData.model!, temperature: parseFloat(e.target.value) }
                    })}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Voice Configuration */}
          <Card className="bg-gradient-to-r from-teal-50 to-green-50 border-teal-100">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-manrope flex items-center gap-2 text-teal-700">
                <Volume2 className="h-5 w-5" />
                Voice Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label className="font-inter font-medium">Voice Provider</Label>
                  <Select 
                    value={formData.voice?.provider}
                    onValueChange={(value: "openai" | "elevenlabs") => setFormData({
                      ...formData,
                      voice: { ...formData.voice!, provider: value, voiceId: availableVoices[value][0] }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="openai">OpenAI</SelectItem>
                      <SelectItem value="elevenlabs">ElevenLabs</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label className="font-inter font-medium">Voice</Label>
                  <Select 
                    value={formData.voice?.voiceId}
                    onValueChange={(value) => setFormData({
                      ...formData,
                      voice: { ...formData.voice!, voiceId: value }
                    })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {availableVoices[formData.voice?.provider || "openai"].map((voice) => (
                        <SelectItem key={voice} value={voice}>{voice}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Functions Configuration */}
          <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-100">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-manrope flex items-center gap-2 text-purple-700">
                <Code className="h-5 w-5" />
                Available Functions
              </CardTitle>
              <CardDescription className="font-inter text-gray-600">
                Add functions that your assistant can call to interact with your freight system
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Current Functions */}
              <div className="space-y-3 mb-4">
                {formData.model?.functions?.map((func, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-white rounded-lg border border-purple-100">
                    <div>
                      <p className="font-inter font-medium">{func.name}</p>
                      <p className="text-sm font-inter text-gray-600">{func.description}</p>
                      <div className="flex gap-1 mt-1">
                        {func.parameters.required.map((param) => (
                          <Badge key={param} variant="outline" className="text-xs">
                            {param}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeFunction(func.name)}
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
                {(!formData.model?.functions || formData.model.functions.length === 0) && (
                  <p className="text-gray-500 font-inter text-center py-4">No functions configured</p>
                )}
              </div>

              {/* Auto-configured Functions Notice */}
              <div className="space-y-3">
                <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-100">
                  <h4 className="font-manrope font-semibold mb-2 text-blue-700 flex items-center gap-2">
                    <Code className="h-4 w-4" />
                    Auto-Configured Functions
                  </h4>
                  <p className="font-inter text-sm text-blue-600 mb-3">
                    Functions are automatically added based on your role and organization settings. All essential freight operations are included by default.
                  </p>
                  <div className="grid gap-2 md:grid-cols-2">
                    {defaultFunctions.map((func) => (
                      <div key={func.name} className="flex items-center gap-2 text-sm">
                        <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-teal-400 rounded-full"></div>
                        <span className="font-inter font-medium">{func.name}</span>
                      </div>
                    ))}
                  </div>
                </div>
                
                {/* Advanced Function Management (for ADMIN only) */}
                <details className="group">
                  <summary className="cursor-pointer font-inter font-medium text-gray-700 hover:text-blue-600 transition-colors">
                    Advanced Function Management (Admin Only)
                  </summary>
                  <div className="mt-3 p-4 bg-amber-50 rounded-lg border border-amber-100">
                    <p className="font-inter text-sm text-amber-700 mb-3">
                      ⚠️ Advanced users can manually manage functions. Most users should keep the auto-configured functions.
                    </p>
                    <div className="grid gap-2 md:grid-cols-2">
                      {defaultFunctions.map((func) => {
                        const isAdded = formData.model?.functions?.some(f => f.name === func.name);
                        return (
                          <Button
                            key={func.name}
                            type="button"
                            variant={isAdded ? "secondary" : "outline"}
                            onClick={() => addFunction(func)}
                            disabled={isAdded}
                            size="sm"
                            className="justify-start h-auto p-2"
                          >
                            <div className="text-left">
                              <p className="font-medium text-xs">{func.name}</p>
                              <p className="text-xs text-gray-600">{func.description.substring(0, 40)}...</p>
                            </div>
                          </Button>
                        );
                      })}
                    </div>
                  </div>
                </details>
              </div>
            </CardContent>
          </Card>

          {/* Server URL */}
          <div className="space-y-2">
            <Label htmlFor="serverUrl" className="font-inter font-medium">Server URL (for function calls)</Label>
            <Input
              id="serverUrl"
              value={formData.serverUrl}
              onChange={(e) => setFormData({...formData, serverUrl: e.target.value})}
              placeholder="https://your-domain.com/api/vapi-tools"
              className="font-mono text-sm"
            />
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button type="button" variant="outline" onClick={onCancel} className="font-inter">
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button type="submit" className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-inter">
              <Save className="h-4 w-4 mr-2" />
              {assistant ? "Update Assistant" : "Create Assistant"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}