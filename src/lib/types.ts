// src/lib/types.ts
export type UserRole = "ADMIN" | "BROKER" | "CARRIER" | "CONSIGNOR" | "CONSIGNEE";

// Authentication types for JWT-based system
export interface JWTToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token?: string;
}

export interface AuthenticatedUser {
  id: number;
  email: string;
  full_name?: string;
  role: UserRole;
  organization_id?: number;
  organization?: Organization;
  permissions: string[];
  created_at: string;
  updated_at: string;
  last_login?: string;
  is_active: boolean;
}

// PIN authentication requirements for different operations
export interface PINRequirement {
  operation: string;
  requires_pin: boolean;
  description: string;
}

export type UrgencyLevel = "routine" | "urgent" | "critical";

export type ShipmentType = "pharmaceutical" | "electronics" | "automotive" | "general";

export type ActionType = 
  | "alert_stakeholders" 
  | "get_solutions" 
  | "expedite" 
  | "schedule_followup"
  | "check_alternatives";

export type StakeholderType = 
  | "consignee" 
  | "warehouse" 
  | "customer_service" 
  | "customs_broker"
  | "finance"
  | "manager";

export interface User {
  id: number;
  email: string;
  full_name?: string;
  role: UserRole;
  organization_id?: number;
  organization?: Organization;
  created_at: string;
  updated_at: string;
}

// Enhanced Organization interface for new authentication system
export interface Organization {
  id: number;
  name: string;
  pin_required_operations: PINRequirement[];
  created_at: string;
  updated_at: string;
  users?: User[];
  settings?: OrganizationSettings;
}

export interface OrganizationSettings {
  pin_policy: {
    enabled: boolean;
    required_for_viewing: boolean;
    required_for_critical_operations: boolean;
    exempt_roles: UserRole[];
  };
  machine_to_machine_auth: {
    enabled: boolean;
    api_key?: string;
  };
}

export interface TrackingUpdate {
  id: number;
  freight_brokerage_id: number;
  status_description: string;
  location?: string;
  timestamp: string;
  notes?: string;
  updated_by_user_id?: number;
  updated_by?: User;
  proof_of_delivery?: string;
  event_code?: string;
}

export interface Shipment {
  id: number;
  tracking_number: string;
  external_reference_number?: string;
  status: string;
  consignor_id: number;
  consignee_id?: number;
  carrier_id?: number;
  consignor?: User;
  consignee?: User;
  carrier?: User;
  origin_address?: string;
  destination_address?: string;
  expected_delivery_date?: string;
  actual_delivery_date?: string;
  shipment_details?: Record<string, unknown>;
  urgency_level?: UrgencyLevel;
  shipment_type?: ShipmentType;
  action_required?: ActionType;
  stakeholder_notifications?: Record<string, unknown>;
  is_insured?: boolean;
  declared_value?: number;
  customs_info?: string;
  created_at: string;
  updated_at: string;
  tracking_updates?: TrackingUpdate[];
}

export interface Solution {
  id: string;
  title: string;
  description: string;
  cost?: number;
  time_savings?: string;
  feasibility: "high" | "medium" | "low";
  created_at: string;
  next_steps?: string[];
}

export interface ShipmentSolution {
  id: number;
  shipment_id: number;
  problem_summary: string;
  recommended_solution_id?: string;
  created_at: string;
  shipment?: Shipment;
  solutions?: Solution[];
  recommended_solution?: Solution;
}

export interface StakeholderNotification {
  id: number;
  stakeholder_type: StakeholderType;
  contact_info: string;
  message: string;
  notification_sent: boolean;
  timestamp: string;
  shipment_id: number;
  shipment?: Shipment;
}

export interface VoiceAction {
  id: number;
  action_type: ActionType;
  shipment_id?: number;
  urgency_level: UrgencyLevel;
  context?: Record<string, unknown>;
  user_message?: string;
  action_taken: string;
  details?: Record<string, unknown>;
  follow_up_needed?: string;
  next_actions?: string[];
  created_at: string;
  shipment?: Shipment;
}

export interface IntelligenceInsight {
  id: number;
  insight_type: string;
  title: string;
  description: string;
  impact_level: "high" | "medium" | "low";
  actionable: boolean;
  suggested_actions?: string[];
  data_points?: Record<string, unknown>;
  shipment_id?: number;
  organization_id?: number;
  created_at: string;
  shipment?: Shipment;
  organization?: Organization;
}

export interface ShipmentStatusResponse {
  shipment: Shipment;
  insights: string;
  voice_optimized_response: string;
}

export interface ExtractedTrackingInfo {
  tracking_numbers: string[];
  reference_numbers: string[];
  context: string;
}

// VAPI Configuration Types
export interface VAPIAssistant {
  id: string;
  name: string;
  description?: string;
  model: {
    provider: 'openai' | 'anthropic';
    model: string;
    temperature?: number;
    functions?: VAPIFunction[];
  };
  voice: {
    provider: 'openai' | 'elevenlabs';
    voiceId: string;
  };
  serverUrl?: string;
  serverUrlSecret?: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
}

export interface VAPIFunction {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, unknown>;
    required: string[];
  };
}

export interface VAPICall {
  id: string;
  assistant_id: string;
  phone_number?: string;
  status: 'queued' | 'ringing' | 'in-progress' | 'forwarding' | 'ended';
  started_at?: string;
  ended_at?: string;
  cost?: number;
  transcript?: string;
  recording_url?: string;
  created_at: string;
}

export interface VAPIWebhookConfig {
  id: string;
  webhook_url: string;
  events: string[];
  secret: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface VAPISettings {
  api_key: string;
  webhook_secret: string;
  default_assistant_id?: string;
  voice_commands_enabled: boolean;
  auto_transcription: boolean;
  webhook_configs: VAPIWebhookConfig[];
}

// Status search response types based on API
export interface StatusSearchShipment {
  tracking_number: string;
  external_reference_number: string;
  status: string;
  origin_address: string;
  destination_address: string;
  expected_delivery_date: string;
  shipment_details?: {
    weight_kg?: number;
    commodity?: string;
    is_international?: boolean;
    container_count?: number;
    requires_temperature_control?: boolean;
    vessel_id?: string;
  } | null;
  id: number;
  created_at: string;
  updated_at: string;
  actual_delivery_date?: string | null;
  consignor: {
    email: string;
    full_name: string;
    phone?: string | null;
    role: string;
    id: number;
    created_at: string;
    updated_at: string;
    organization_id: number;
  };
  consignee: {
    email: string;
    full_name: string;
    phone?: string | null;
    role: string;
    id: number;
    created_at: string;
    updated_at: string;
    organization_id: number;
  };
  carrier: {
    email: string;
    full_name: string;
    phone?: string | null;
    role: string;
    id: number;
    created_at: string;
    updated_at: string;
    organization_id: number;
  };
  tracking_updates: TrackingUpdateDetail[];
}

export interface TrackingUpdateDetail {
  event_code: string;
  status_description: string;
  location: string;
  notes?: string | null;
  event_timestamp?: string | null;
  proof_of_delivery?: string | null;
  id: number;
  freight_brokerage_id: number;
  timestamp: string;
  updated_by_user_id?: number | null;
}
