// src/lib/api.ts

/**
 * UPDATED: This module is now integrated with JWT authentication but is still being phased out
 * in favor of PaceAPIClient in contexts/OrganizationContext.tsx for better organization context.
 * 
 * New code should use the apiClient from useOrganizationContext() for all API calls to ensure
 * proper organization context headers and role-based access control are included.
 * 
 * Example usage:
 *   const { apiClient } = useOrganizationContext();
 *   apiClient.get('/some/endpoint');
 */

import axios, { AxiosResponse, AxiosError, InternalAxiosRequestConfig } from "axios";
import { Shipment, Organization, User, TrackingUpdate, JWTToken } from "./types";
import { trackApiError, trackPerformance } from "./analytics";

// Types for API error tracking
interface ApiErrorData extends Record<string, unknown> {
  statusCode?: number;
  method: string;
  message: string;
  data?: unknown;
}

// Extend AxiosRequestConfig to include _retry property for token refresh
interface ExtendedAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
}

// You should replace this with your actual API URL
const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";// Custom error type for API errors


// Create an axios instance with baseURL and default headers
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Helper function to get JWT tokens from localStorage
const getAuthTokens = (): JWTToken | null => {
  if (typeof window === "undefined") return null;
  
  try {
    const tokenData = localStorage.getItem("jwtToken");
    return tokenData ? JSON.parse(tokenData) : null;
  } catch {
    return null;
  }
};

// Helper function to refresh access token
const refreshAccessToken = async (): Promise<string | null> => {
  try {
    const tokens = getAuthTokens();
    if (!tokens?.refresh_token) return null;

    const response = await axios.post(`${API_URL}/api/v1/auth/refresh`, {
      refresh_token: tokens.refresh_token
    });

    const newTokens: JWTToken = response.data;
    localStorage.setItem("jwtToken", JSON.stringify(newTokens));
    return newTokens.access_token;
  } catch {
    // Clear tokens and redirect to login
    localStorage.removeItem("jwtToken");
    localStorage.removeItem("currentUser");
    return null;
  }
};

// Add a request interceptor to include JWT auth token and role headers
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Add timestamp to track API performance
    if (config.headers) {
      config.headers['x-request-start-time'] = Date.now();
    }
    
    if (typeof window !== "undefined") {
      const tokens = getAuthTokens();
      
      if (tokens?.access_token && config.headers) {
        config.headers.Authorization = `Bearer ${tokens.access_token}`;
      }

      // Add user role for role-based access control
      const userData = localStorage.getItem("currentUser");
      if (userData) {
        try {
          const user = JSON.parse(userData);
          if (user.role && config.headers) {
            config.headers["X-User-Role"] = user.role;
          }
        } catch {
          // Ignore parsing errors
        }
      }

      // Add organization context if available
      const orgId = localStorage.getItem("selectedOrganizationId");
      const orgPin = localStorage.getItem("organizationPin");
      
      if (orgId && config.headers) {
        config.headers["X-Organization-ID"] = orgId;
        if (orgPin) {
          config.headers["X-Organization-PIN"] = orgPin;
        }
      }
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle JWT token refresh and response format inconsistencies
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Track API performance
    const requestTime = response.config.headers?.['x-request-start-time'];
    const url = response.config.url || '';
    const method = response.config.method?.toUpperCase() || 'UNKNOWN';
    
    if (requestTime && typeof requestTime === 'number') {
      const duration = Date.now() - requestTime;
      trackPerformance(`API ${method} ${url}`, duration, { 
        statusCode: response.status,
        method,
      });
    }
    
    // Ensure response.data is always present and handle various API response formats
    if (response.data === undefined || response.data === null) {
      response.data = [];
    }

    // If the API returns a format like { success: true, data: [...] }, extract the data
    if (response.data && typeof response.data === 'object' && 'data' in response.data) {
      response.data = response.data.data;
    }
    
    // Ensure arrays are always arrays
    if (!Array.isArray(response.data) && typeof response.data === 'object') {
      // If it's a single object, wrap it in an array for list endpoints
      const isListEndpoint = response.config?.url?.includes('/organization/') || 
                            response.config?.url?.includes('/user/') || 
                            response.config?.url?.includes('/status/');
      
      if (isListEndpoint && response.config?.method === 'get' && !response.config?.url?.match(/\/\d+$/)) {
        response.data = [response.data];
      }
    }
    
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as ExtendedAxiosRequestConfig;
    
    // Track API errors in LogRocket for analysis
    if (typeof window !== 'undefined') {
      const url = originalRequest?.url || 'unknown';
      const method = originalRequest?.method?.toUpperCase() || 'UNKNOWN';
      const statusCode = error.response?.status;
      
      // Track this error with proper typing
      const errorData: ApiErrorData = {
        statusCode,
        method,
        message: error.message,
        data: error.response?.data
      };
      trackApiError(`${method} ${url}`, errorData);
    }

    // Handle 401 unauthorized - try to refresh token
    if (error.response?.status === 401 && originalRequest && !originalRequest._retry) {
      originalRequest._retry = true;

      const newAccessToken = await refreshAccessToken();
      if (newAccessToken && originalRequest.headers) {
        originalRequest.headers.Authorization = `Bearer ${newAccessToken}`;
        return api(originalRequest);
      }
    }

    // Handle error responses consistently
    if (error.response?.status === 404) {
      // For 404 errors on list endpoints, return empty array
      if (error.config?.method === 'get') {
        return Promise.resolve({ 
          data: [], 
          status: 404, 
          statusText: 'Not Found',
          config: error.config,
          headers: error.response.headers 
        });
      }
    }
    
    return Promise.reject(error);
  }
);

// API functions for different endpoints

// Authentication endpoints (JWT-based)
export const loginUser = async (credentials: { email: string; password: string }) => {
  const formData = new URLSearchParams();
  formData.append('username', credentials.email);
  formData.append('password', credentials.password);

  const response = await fetch('https://api.gopace.app/api/v1/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    body: formData
  });

  if (!response.ok) {
    throw new Error('Login failed');
  }

  return await response.json();
};

export const refreshToken = (refreshToken: string) =>
  api.post('/api/v1/auth/refresh', { refresh_token: refreshToken });

export const logoutUser = () =>
  api.post('/api/v1/auth/logout');

export const getCurrentUser = () =>
  api.get('/api/v1/auth/me');

// Role-based user creation
export const createUserWithRole = (data: Partial<User> & { role: string }) =>
  api.post('/api/v1/auth/register', data);

// Status and tracking
export const getStatusById = (brokerageId: string) => 
  api.get(`/api/v1/status/${brokerageId}`);

export const searchByTrackingNumber = (trackingNumber: string) =>
  api.get(`/api/v1/status/search?tracking_number=${trackingNumber}`);

export const createStatus = (data: Partial<Shipment>) =>
  api.post('/api/v1/status/', data);

export const updateStatus = (brokerageId: string, data: Partial<Shipment>) =>
  api.put(`/api/v1/status/${brokerageId}`, data);

export const addTrackingUpdate = (brokerageId: string, data: Partial<TrackingUpdate>) =>
  api.post(`/api/v1/status/${brokerageId}/tracking`, data);

export const getTrackingHistory = (brokerageId: string) =>
  api.get(`/api/v1/status/${brokerageId}/tracking`);

export const getShipmentStatus = (identifier: string) =>
  api.get(`/api/v1/shipment/${identifier}`);

// Organization management
export const createOrganization = (data: Partial<Organization>) =>
  api.post('/api/v1/organization/', data);

export const listOrganizations = () =>
  api.get('/api/v1/organization/');

export const getOrganizationDetails = (orgId: string) =>
  api.get(`/api/v1/organization/${orgId}`);

export const updateOrganization = (orgId: string, data: Partial<Organization>) =>
  api.put(`/api/v1/organization/${orgId}`, data);

export const deleteOrganization = (orgId: string) =>
  api.delete(`/api/v1/organization/${orgId}`);

export const getOrganizationWithUsers = (orgId: string) =>
  api.get(`/api/v1/organization/${orgId}/users`);

export const verifyOrganizationPin = (orgId: string, pin: string) =>
  api.post(`/api/v1/organization/${orgId}/verify-pin`, { pin });

// User management
export const createUser = (data: Partial<User>) =>
  api.post('/api/v1/user/', data);

export const listUsers = (organizationId?: string) => {
  const params = organizationId ? { organization_id: organizationId } : undefined;
  return api.get('/api/v1/user/', { params });
};

export const getUserOrganizations = (userId: string) =>
  api.get(`/api/v1/user/${userId}/organizations`);

export const getUserDetails = (userId: string) =>
  api.get(`/api/v1/user/${userId}`);

export const updateUser = (userId: string, data: Partial<User>) =>
  api.put(`/api/v1/user/${userId}`, data);

export const deleteUser = (userId: string) =>
  api.delete(`/api/v1/user/${userId}`);

export const updateUserRole = (userId: string, role: string) =>
  api.put(`/api/v1/user/${userId}/role`, { role });

export const getUserShipments = (userId: string) =>
  api.get(`/api/v1/user/${userId}/shipments`);

export const assignUserToOrganization = (userId: string, orgId: string) =>
  api.post(`/api/v1/user/${userId}/organizations/${orgId}`);

// Enhanced shipment endpoints with role-based access
export const getEnhancedShipments = (params?: Record<string, unknown>) =>
  api.get('/api/v1/shipments/enhanced', { params });

export const getShipmentsByRole = (role: string, params?: Record<string, unknown>) =>
  api.get(`/api/v1/shipments/by-role/${role}`, { params });

// Organization insights and analytics
export const getOrganizationInsights = (orgId: string) =>
  api.get(`/api/v1/organization/${orgId}/insights`);

export const getOrganizationAnalytics = (orgId: string, dateRange?: string) =>
  api.get(`/api/v1/organization/${orgId}/analytics`, { 
    params: dateRange ? { date_range: dateRange } : undefined 
  });

// PIN requirement checking for selective authentication
export const checkPINRequirement = (action: string, context?: Record<string, unknown>) =>
  api.post('/api/v1/auth/check-pin-requirement', { action, context });

// New tracking API endpoints
export const getTrackingByNumber = (trackingNumber: string) =>
  api.get(`/api/v1/tracking/${trackingNumber}`);

export const getTrackingByBrokerageId = (brokerageId: string) =>
  api.get(`/api/v1/status/${brokerageId}/tracking`);

export const addTrackingEventByNumber = (trackingNumber: string, data: Partial<TrackingUpdate>) =>
  api.post(`/api/v1/tracking/?tracking_number=${trackingNumber}`, data);

export const addTrackingEventToBrokerage = (brokerageId: string, data: Partial<TrackingUpdate>) =>
  api.post(`/api/v1/status/${brokerageId}/tracking`, data);

// Voice and NLP integration with machine-to-machine auth
export const getVoiceEnhancedTracking = (trackingNumber: string) =>
  api.get(`/api/v1/voice-enhanced/${trackingNumber}`);

export const getVoiceStatus = (referenceNumber: string) =>
  api.get(`/api/v1/voice/status/${referenceNumber}`);

export const processVoiceCommand = (data: Record<string, unknown>) =>
  api.post('/api/v1/voice/process', data);

export const extractTrackingNumbers = (text: string) =>
  api.post('/api/v1/nlp/extract', { text });

// Celery task management endpoints
export const getCeleryTaskStatus = (taskId: string) =>
  api.get(`/api/v1/tasks/${taskId}/status`);

export const triggerCeleryTask = (taskName: string, params?: Record<string, unknown>) =>
  api.post('/api/v1/tasks/trigger', { task_name: taskName, params });

export const getCeleryTaskHistory = (limit = 50) =>
  api.get(`/api/v1/tasks/history`, { params: { limit } });

// Enhanced error handling and role-based error messages
export const getApiHealth = () =>
  api.get('/api/v1/health');

export const checkRolePermissions = (action: string) =>
  api.get(`/api/v1/auth/permissions/${action}`);

export default api;
