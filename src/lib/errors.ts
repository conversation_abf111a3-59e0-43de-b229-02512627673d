
// lib/errors.ts
export class APIError extends Error {
  status: number;
  details: Record<string, unknown>;

  constructor(message: string, status: number, details: Record<string, unknown> = {}) {
    super(message);
    this.name = 'APIError';
    this.status = status;
    this.details = details;
  }
}

/**
 * Centralized API response handler as recommended in the Frontend Integration Guide
 * Processes API responses, handles errors, and normalizes data formats
 */
export async function handleAPIResponse(response: Response) {
  // For 204 No Content responses, return null immediately
  if (response.status === 204) {
    return null;
  }
  
  // Handle error responses
  if (!response.ok) {
    let errorData;
    try {
      // Try to parse as JSON but handle text responses too
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        errorData = await response.json();
      } else {
        const text = await response.text();
        errorData = { message: text || 'Unknown error occurred' };
      }
    } catch {
      errorData = { message: 'Failed to parse error response' };
    }
    
    // Construct meaningful error message
    const errorMessage = errorData.message || 
                        errorData.error || 
                        `HTTP Error ${response.status}: ${response.statusText}`;
    
    switch (response.status) {
      case 401: // Unauthorized
        throw new APIError('Authentication failed. Please log in again.', response.status, errorData);
      case 403: // Forbidden
        throw new APIError('Access denied. Check organization credentials and PIN.', response.status, errorData);
      case 404: // Not Found
        throw new APIError('The requested resource was not found.', response.status, errorData);
      case 400: // Bad Request
        throw new APIError('Invalid request data. Please check your input.', response.status, errorData);
      case 422: // Unprocessable Entity
        throw new APIError('Request validation failed. Check required headers (X-Organization-ID, X-Organization-PIN).', response.status, errorData);
      case 500: // Internal Server Error
        throw new APIError('An unexpected error occurred on the server.', response.status, errorData);
      case 503: // Service Unavailable
        throw new APIError('Service temporarily unavailable. Please try again later.', response.status, errorData);
      default:
        throw new APIError(`HTTP Error ${response.status}: ${errorMessage}`, response.status, errorData);
    }
  }
  
  // If response is OK but has no content (e.g., 204 No Content)
  if (response.status === 204) {
    return null; 
  }

  // Try to parse JSON, but handle cases where it might not be JSON (e.g. plain text response)
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  } else {
    return response.text(); // Or handle as appropriate for your API
  }
}
