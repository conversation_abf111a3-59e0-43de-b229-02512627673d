import LogRocket from 'logrocket';

/**
 * Initialize LogRocket for analytics tracking
 * This should be called on the client side only and only in production
 */
export const initializeAnalytics = () => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    LogRocket.init('isrc8k/gopace', {
      // Additional configuration options can be added here
      // https://docs.logrocket.com/reference/init
      release: process.env.NEXT_PUBLIC_APP_VERSION,
      console: {
        shouldAggregateConsoleErrors: true,
      },
      network: {
        // Capture network requests and responses
        requestSanitizer: (request) => {
          // Don't record sensitive request data
          if (request.headers.authorization) {
            request.headers.authorization = '**removed**';
          }
          return request;
        },
        responseSanitizer: (response) => {
          // You can sanitize sensitive data in responses
          return response;
        },
      },
    });

    console.log('LogRocket initialized successfully');
  } else if (typeof window !== 'undefined') {
    console.log('LogRocket disabled in development mode');
  }
};

/**
 * Identify the current user in LogRocket
 * Call this after user authentication - only in production
 */
export const identifyUser = (user: { id: string | number, email: string, full_name?: string, role?: string }) => {
  if (typeof window !== 'undefined' && user && process.env.NODE_ENV === 'production') {
    LogRocket.identify(String(user.id), {
      name: user.full_name || user.email,
      email: user.email,
      role: user.role || 'unknown',
      // Add any other user metadata you want to track
    });
  }
};

/**
 * Track page views in your application
 * Call this when a page is loaded - only in production
 */
export const trackPageView = (pageName: string, properties?: Record<string, string | number | boolean>) => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    LogRocket.track('Page View', {
      page: pageName,
      url: window.location.href,
      ...properties
    });
  }
};

/**
 * Track feature usage in the app - only in production
 */
export const trackFeatureUsage = (featureName: string, properties?: Record<string, string | number | boolean>) => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    LogRocket.track('Feature Used', {
      feature: featureName,
      ...properties
    });
  }
};

/**
 * Track performance metrics manually - only in production
 */
export const trackPerformance = (eventName: string, durationMs: number, properties?: Record<string, string | number | boolean>) => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    LogRocket.track('Performance Metric', {
      event: eventName,
      durationMs,
      ...properties
    });
  }
};

/**
 * Track API errors - only in production
 */
export const trackApiError = (
  endpoint: string, 
  error: Error | Record<string, unknown>, 
  properties?: Record<string, string | number | boolean>
) => {
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
    LogRocket.track('API Error', {
      endpoint,
      error: error instanceof Error ? error.message : String(error),
      ...properties
    });
  }
};
