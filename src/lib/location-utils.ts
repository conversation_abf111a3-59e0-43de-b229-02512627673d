// Location extraction utility for tracking updates
// Handles various field names and formats for location data

export interface LocationExtraction {
  location: string | null;
  confidence: 'high' | 'medium' | 'low';
  source: string;
}

/**
 * Extract location information from a tracking update object
 * Tries multiple field names and extraction strategies
 */
export function extractLocation(update: Record<string, unknown>): LocationExtraction {  if (!update || typeof update !== 'object') {
    return { location: null, confidence: 'low', source: 'no_data' };
  }

  // High confidence fields - direct location data
  const highConfidenceFields = [
    'location',
    'current_location', 
    'address',
    'full_address',
    'location_name',
    'place_name'
  ];

  for (const field of highConfidenceFields) {
    const value = update[field];
    if (typeof value === 'string' && value.trim()) {
      return { 
        location: value.trim(), 
        confidence: 'high', 
        source: field 
      };
    }
  }

  // Medium confidence - composite location fields
  const cityState = buildCityState(update);
  if (cityState) {
    return { 
      location: cityState, 
      confidence: 'medium', 
      source: 'city_state_composite' 
    };
  }

  const portFacility = extractPortOrFacility(update);
  if (portFacility) {
    return { 
      location: portFacility, 
      confidence: 'medium', 
      source: 'port_facility' 
    };
  }

  // Medium confidence - geographic fields
  const mediumConfidenceFields = [
    'city',
    'port',
    'facility',
    'terminal',
    'warehouse',
    'depot',
    'hub'
  ];

  for (const field of mediumConfidenceFields) {
    const value = update[field];
    if (typeof value === 'string' && value.trim()) {
      return { 
        location: value.trim(), 
        confidence: 'medium', 
        source: field 
      };
    }
  }

  // Low confidence - extract from text fields
  const textExtraction = extractFromText(update);
  if (textExtraction) {
    return { 
      location: textExtraction, 
      confidence: 'low', 
      source: 'text_extraction' 
    };
  }

  return { location: null, confidence: 'low', source: 'not_found' };
}

/**
 * Build location from city, state, country fields
 */
function buildCityState(update: Record<string, unknown>): string | null {  const city = typeof update.city === 'string' ? update.city.trim() : null;
  const state = typeof update.state === 'string' ? update.state.trim() : null;
  const country = typeof update.country === 'string' ? update.country.trim() : null;

  if (city && state && country) {
    return `${city}, ${state}, ${country}`;
  }
  if (city && state) {
    return `${city}, ${state}`;
  }
  if (city && country) {
    return `${city}, ${country}`;
  }
  if (state && country) {
    return `${state}, ${country}`;
  }
  
  return null;
}

/**
 * Extract port or facility names
 */
function extractPortOrFacility(update: Record<string, unknown>): string | null {
  const facilityFields = [
    'port_name',
    'port_code',
    'facility_name',
    'facility_code',
    'terminal_name',
    'warehouse_name',
    'depot_name'
  ];

  for (const field of facilityFields) {
    const value = update[field];
    if (typeof value === 'string' && value.trim()) {
      return value.trim();
    }
  }

  return null;
}

/**
 * Extract location from descriptive text fields
 */
function extractFromText(update: Record<string, unknown>): string | null {
  const textFields = [
    'status_description',
    'description', 
    'notes',
    'comments',
    'details'
  ];

  for (const field of textFields) {
    const text = update[field];
    if (!text || typeof text !== 'string') continue;

    // Look for patterns like "at [location]" or "in [location]"
    const atMatch = text.match(/\s+at\s+([^,\.\n]+)/i);
    if (atMatch && atMatch[1]) {
      return atMatch[1].trim();
    }

    const inMatch = text.match(/\s+in\s+([^,\.\n]+)/i);
    if (inMatch && inMatch[1]) {
      return inMatch[1].trim();
    }

    // Look for patterns like "arrived [location]" or "departed [location]"
    const arrivedMatch = text.match(/(?:arrived|reached|entered)\s+([^,\.\n]+)/i);
    if (arrivedMatch && arrivedMatch[1]) {
      return arrivedMatch[1].trim();
    }

    const departedMatch = text.match(/(?:departed|left|exited)\s+([^,\.\n]+)/i);
    if (departedMatch && departedMatch[1]) {
      return departedMatch[1].trim();
    }
  }

  return null;
}

/**
 * Get a formatted location string with fallback handling
 */
export function getDisplayLocation(update: Record<string, unknown>): string {
  const extraction = extractLocation(update);
  
  if (extraction.location) {
    return extraction.location;
  }

  // Smart fallback based on status
  const statusDesc = update.status_description;
  const status = update.status;
  const statusText = (typeof statusDesc === 'string' ? statusDesc : typeof status === 'string' ? status : '').toLowerCase();
  
  if (statusText.includes('transit')) {
    return 'In Transit';
  }
  if (statusText.includes('warehouse')) {
    return 'Warehouse Facility';
  }
  if (statusText.includes('port')) {
    return 'Port Terminal';
  }
  if (statusText.includes('customs')) {
    return 'Customs Facility';
  }
  if (statusText.includes('picked')) {
    return 'Origin Location';
  }
  if (statusText.includes('delivered')) {
    return 'Destination';
  }

  return 'Location Not Available';
}

/**
 * Debug function to analyze what location fields are available
 */
export function debugLocationFields(update: Record<string, unknown>): void {
  console.log('🔍 Analyzing location fields for update:', update.id || 'unknown');
  console.log('📍 Available fields:', Object.keys(update));
  
  const extraction = extractLocation(update);
  console.log('🎯 Extraction result:', extraction);
  
  // Log all potentially location-related fields
  const locationFields = Object.keys(update).filter(key => 
    key.toLowerCase().includes('location') ||
    key.toLowerCase().includes('address') ||
    key.toLowerCase().includes('city') ||
    key.toLowerCase().includes('port') ||
    key.toLowerCase().includes('facility')
  );
  
  console.log('🗺️ Location-related fields:', locationFields.map(field => 
    `${field}: ${update[field]}`
  ));
}