// VAPI Service for managing voice assistants and calls
import { VAPIAssistant, VAPICall, VAPIFunction } from './types';

export class VAPIService {
  private baseUrl = 'https://api.vapi.ai';
  private apiKey: string;

  constructor(apiKey?: string) {
    this.apiKey = apiKey || process.env.NEXT_PUBLIC_VAPI_API_KEY || '';
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${this.baseUrl}${endpoint}`;
    const headers = {
      'Authorization': `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      ...options.headers,
    };

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      if (!response.ok) {
        throw new Error(`VAPI API error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('VAPI request failed:', error);
      throw error;
    }
  }

  // Assistant Management
  async createAssistant(assistantData: Partial<VAPIAssistant>): Promise<VAPIAssistant> {
    const payload = {
      name: assistantData.name,
      model: assistantData.model,
      voice: assistantData.voice,
      ...(assistantData.serverUrl && { serverUrl: assistantData.serverUrl }),
      ...(assistantData.serverUrlSecret && { serverUrlSecret: assistantData.serverUrlSecret }),
    };

    return this.makeRequest('/assistant', {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  }

  async updateAssistant(assistantId: string, assistantData: Partial<VAPIAssistant>): Promise<VAPIAssistant> {
    const payload = {
      name: assistantData.name,
      model: assistantData.model,
      voice: assistantData.voice,
      ...(assistantData.serverUrl && { serverUrl: assistantData.serverUrl }),
      ...(assistantData.serverUrlSecret && { serverUrlSecret: assistantData.serverUrlSecret }),
    };

    return this.makeRequest(`/assistant/${assistantId}`, {
      method: 'PATCH',
      body: JSON.stringify(payload),
    });
  }

  async getAssistant(assistantId: string): Promise<VAPIAssistant> {
    return this.makeRequest(`/assistant/${assistantId}`);
  }

  async listAssistants(): Promise<VAPIAssistant[]> {
    return this.makeRequest('/assistant');
  }

  async deleteAssistant(assistantId: string): Promise<void> {
    return this.makeRequest(`/assistant/${assistantId}`, {
      method: 'DELETE',
    });
  }

  // Call Management
  async createCall(phoneNumber: string, assistantId: string, context?: Record<string, unknown>): Promise<VAPICall> {
    const payload: Record<string, unknown> = {
      phoneNumber,
      assistant: { id: assistantId },
    };
    
    if (context) {
      payload.assistantOverrides = { variableValues: context };
    }

    return this.makeRequest('/call', {
      method: 'POST',
      body: JSON.stringify(payload),
    });
  }

  async getCall(callId: string): Promise<VAPICall> {
    return this.makeRequest(`/call/${callId}`);
  }

  async listCalls(): Promise<VAPICall[]> {
    return this.makeRequest('/call');
  }

  async endCall(callId: string): Promise<void> {
    return this.makeRequest(`/call/${callId}`, {
      method: 'DELETE',
    });
  }

  // Get role-based functions for different user types
  getRoleBasedFunctions(userRole: string): VAPIFunction[] {
    const baseFunctions: VAPIFunction[] = [
      {
        name: "get_shipment_status",
        description: "Get real-time shipment tracking information and status updates",
        parameters: {
          type: "object",
          properties: {
            tracking_number: { 
              type: "string", 
              description: "The tracking number of the shipment" 
            }
          },
          required: ["tracking_number"]
        }
      }
    ];

    const adminFunctions: VAPIFunction[] = [
      ...baseFunctions,
      {
        name: "create_shipment_alert",
        description: "Create alerts for shipment delays or issues",
        parameters: {
          type: "object",
          properties: {
            tracking_number: { 
              type: "string", 
              description: "The tracking number of the shipment" 
            },
            alert_type: { 
              type: "string", 
              enum: ["delay", "damage", "customs_hold", "delivery_attempt"], 
              description: "Type of alert to create" 
            },
            message: { 
              type: "string", 
              description: "Custom alert message" 
            }
          },
          required: ["tracking_number", "alert_type"]
        }
      },
      {
        name: "notify_stakeholders",
        description: "Send notifications to relevant stakeholders about shipment updates",
        parameters: {
          type: "object",
          properties: {
            tracking_number: { 
              type: "string", 
              description: "The tracking number of the shipment" 
            },
            stakeholder_types: { 
              type: "array", 
              items: { type: "string" },
              description: "Types of stakeholders to notify (consignee, warehouse, customer_service, etc.)" 
            },
            message: { 
              type: "string", 
              description: "Notification message" 
            }
          },
          required: ["tracking_number", "stakeholder_types", "message"]
        }
      },
      {
        name: "generate_report",
        description: "Generate operational reports and analytics",
        parameters: {
          type: "object",
          properties: {
            report_type: {
              type: "string",
              enum: ["daily_summary", "weekly_performance", "exception_report"],
              description: "Type of report to generate"
            },
            date_range: {
              type: "string",
              description: "Date range for the report (e.g., 'last 7 days')"
            }
          },
          required: ["report_type"]
        }
      }
    ];

    const brokerFunctions: VAPIFunction[] = [
      ...baseFunctions,
      {
        name: "create_shipment_alert",
        description: "Create alerts for shipment delays or issues",
        parameters: {
          type: "object",
          properties: {
            tracking_number: { 
              type: "string", 
              description: "The tracking number of the shipment" 
            },
            alert_type: { 
              type: "string", 
              enum: ["delay", "damage", "customs_hold", "delivery_attempt"], 
              description: "Type of alert to create" 
            },
            message: { 
              type: "string", 
              description: "Custom alert message" 
            }
          },
          required: ["tracking_number", "alert_type"]
        }
      },
      {
        name: "notify_stakeholders",
        description: "Send notifications to relevant stakeholders about shipment updates",
        parameters: {
          type: "object",
          properties: {
            tracking_number: { 
              type: "string", 
              description: "The tracking number of the shipment" 
            },
            stakeholder_types: { 
              type: "array", 
              items: { type: "string" },
              description: "Types of stakeholders to notify" 
            },
            message: { 
              type: "string", 
              description: "Notification message" 
            }
          },
          required: ["tracking_number", "stakeholder_types", "message"]
        }
      }
    ];

    const carrierFunctions: VAPIFunction[] = [
      ...baseFunctions,
      {
        name: "update_delivery_status",
        description: "Update delivery status and location",
        parameters: {
          type: "object",
          properties: {
            tracking_number: { 
              type: "string", 
              description: "The tracking number of the shipment" 
            },
            status: {
              type: "string",
              enum: ["picked_up", "in_transit", "out_for_delivery", "delivered", "exception"],
              description: "Current delivery status"
            },
            location: {
              type: "string",
              description: "Current location or delivery address"
            },
            notes: {
              type: "string",
              description: "Additional notes or comments"
            }
          },
          required: ["tracking_number", "status"]
        }
      }
    ];

    switch (userRole.toUpperCase()) {
      case 'ADMIN':
        return adminFunctions;
      case 'BROKER':
        return brokerFunctions;
      case 'CARRIER':
        return carrierFunctions;
      case 'CONSIGNOR':
      case 'CONSIGNEE':
        return baseFunctions;
      default:
        return baseFunctions;
    }
  }

  // Helper method to create a freight assistant with default configuration
  async createFreightAssistant(name: string, description?: string, userRole: string = 'BROKER'): Promise<VAPIAssistant> {
    const assistantData: Partial<VAPIAssistant> = {
      name,
      description: description || "AI assistant for freight and logistics operations",
      model: {
        provider: "openai",
        model: "gpt-4",
        temperature: 0.7,
        // Automatically configure functions based on user role
        functions: this.getRoleBasedFunctions(userRole)
      },
      voice: {
        provider: "openai",
        voiceId: "alloy"
      },
      serverUrl: process.env.NODE_ENV === 'development' 
        ? "http://localhost:3001/api/vapi-tools" 
        : "https://your-domain.com/api/vapi-tools",
    };

    return this.createAssistant(assistantData);
  }

  // Test connection to VAPI
  async testConnection(): Promise<boolean> {
    try {
      await this.listAssistants();
      return true;
    } catch (error) {
      console.error('VAPI connection test failed:', error);
      return false;
    }
  }
}

// Export a singleton instance for global use
export const vapiService = new VAPIService();

// Helper function to extract tracking numbers from voice input
export function extractTrackingNumbers(text: string): string[] {
  // Common tracking number patterns
  const patterns = [
    /\b(?:PACE|pace)\s*[A-Z0-9]{6,12}\b/gi,  // PACE format
    /\b[A-Z0-9]{6,12}\b/g,                    // Generic alphanumeric
    /\b\d{10,22}\b/g,                         // Pure numeric (FedEx, UPS, etc.)
  ];

  const matches = new Set<string>();
  
  patterns.forEach(pattern => {
    const found = text.match(pattern);
    if (found) {
      found.forEach(match => matches.add(match.trim().toUpperCase()));
    }
  });

  return Array.from(matches);
}