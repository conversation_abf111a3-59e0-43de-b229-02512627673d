// src/lib/stores/dashboard-store.ts
import { create } from 'zustand';
import { 
  Organization, 
  User, 
  Shipment, 
  ShipmentSolution,
  IntelligenceInsight,
  StatusSearchShipment,
  UserRole,
  ShipmentType
} from '@/lib/types';
import { PaceAPIClient } from '../api-client';

interface ChartData {
  labels: string[];
  datasets: Array<{
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    fill?: boolean;
  }>;
}

interface ShipmentStatistics {
  total: number;
  active: number;
  delivered: number;
  delayed: number;
}

interface DashboardMetrics {
  total_shipments: number;
  pending_shipments: number;
  in_transit_shipments: number;
  delivered_shipments: number;
  delayed_shipments: number;
  on_time_delivery_rate: number;
  total_revenue?: number; // Only for ADMIN/BROKER
  average_transit_time: number;
  total_quotes: number;
  quote_acceptance_rate: number;
  period_days: number;
}

interface DashboardPerformance {
  current_period_shipments: number;
  previous_period_shipments: number;
  shipment_growth_rate: number;
  current_on_time_rate: number;
  previous_on_time_rate: number;
  on_time_improvement: number;
}

interface DashboardRevenue {
  total_revenue: number;
  average_shipment_value: number;
  daily_revenue: Array<{
    date: string;
    revenue: number;
  }>;
  top_revenue_routes: Array<{
    route: string;
    revenue: number;
  }>;
}

interface RecentActivityItem {
  id: number;
  type: string;
  description: string;
  timestamp: string;
  related_id: number;
  status: string;
}

interface TopCarrier {
  id: number;
  name: string;
  total_shipments: number;
  on_time_percentage: number;
  total_revenue: number;
  average_rating: number;
  contact_email?: string;
  phone?: string;
}

interface TrackingDetails {
  tracking_number: string;
  carrier: string;
  status: string;
  origin: string;
  destination: string;
  estimated_delivery: string | null;
  updates: Array<{
    timestamp: string;
    location: string;
    status: string;
    details: string;
  }>;
}

interface PerformanceData {
  on_time_delivery_rate: number;
  average_delivery_time: number;
  shipment_volume_growth: number;
  customer_satisfaction: number;
  cost_efficiency: number;
  period_start: string;
  period_end: string;
}

interface DashboardState {
  organizations: Organization[];
  users: User[];
  recentShipments: Shipment[];
  insights: IntelligenceInsight[];
  solutions: ShipmentSolution[];
  statistics: ShipmentStatistics;
  chartData: ChartData;
  metrics: DashboardMetrics | null;
  performance: DashboardPerformance | null;
  revenue: DashboardRevenue | null;
  recentActivity: RecentActivityItem[];
  topCarriers: TopCarrier[];
  performanceData: PerformanceData | null;
  quotes: unknown[];
  isLoading: boolean;
  error: string | null;
  fetchDashboardData: (apiClient: PaceAPIClient) => Promise<void>;
  fetchShipments: (apiClient: PaceAPIClient) => Promise<void>;
  fetchOrganizations: (apiClient: PaceAPIClient) => Promise<void>;
  fetchUsers: (apiClient: PaceAPIClient) => Promise<void>;
  fetchTopCarriers: (apiClient: PaceAPIClient) => Promise<void>;
  fetchPerformanceData: (apiClient: PaceAPIClient) => Promise<void>;
  fetchTrackingDetails: (apiClient: PaceAPIClient, trackingNumber: string) => Promise<TrackingDetails | null>;
  fetchQuotes: (apiClient: PaceAPIClient) => Promise<void>;
  clearError: () => void;
}

export const useDashboardStore = create<DashboardState>((set, get) => ({
  organizations: [],
  users: [],
  recentShipments: [],
  insights: [],
  solutions: [],
  statistics: {
    total: 0,
    active: 0,
    delivered: 0,
    delayed: 0,
  },
  chartData: {
    labels: [] as string[],
    datasets: [] as Array<{
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
      fill?: boolean;
    }>
  },
  metrics: null,
  performance: null,
  revenue: null,
  recentActivity: [],
  topCarriers: [],
  performanceData: null,
  quotes: [],
  isLoading: false,
  error: null,

  clearError: () => set({ error: null }),
  
  fetchDashboardData: async (apiClient: PaceAPIClient) => {
    if (!apiClient) {
      set({ error: "API client is not available. Please check authentication.", isLoading: false });
      return;
    }
    set({ isLoading: true, error: null });
    try {
      // Use new dashboard endpoints to fetch data in parallel
      const [metricsData, revenueData, performanceData, recentActivityData] = await Promise.all([
        apiClient.getDashboardMetrics(30),
        apiClient.getDashboardRevenue(30),
        apiClient.getDashboardPerformance(30),
        apiClient.getRecentActivity(10),
      ]);

      // Process metrics data - use exact API field names
      const metrics: DashboardMetrics | null = metricsData ? {
        total_shipments: metricsData.total_shipments || 0,
        pending_shipments: metricsData.pending_shipments || 0,
        in_transit_shipments: metricsData.in_transit_shipments || 0,
        delivered_shipments: metricsData.delivered_shipments || 0,
        delayed_shipments: metricsData.delayed_shipments || 0,
        on_time_delivery_rate: metricsData.on_time_delivery_rate || 0,
        total_revenue: metricsData.total_revenue || 0,
        average_transit_time: metricsData.average_transit_time || 0,
        total_quotes: metricsData.total_quotes || 0,
        quote_acceptance_rate: metricsData.quote_acceptance_rate || 0,
        period_days: metricsData.period_days || 30,
      } : null;

      // Process performance data - use exact API field names
      const performance: DashboardPerformance | null = performanceData ? {
        current_period_shipments: performanceData.current_period_shipments || 0,
        previous_period_shipments: performanceData.previous_period_shipments || 0,
        shipment_growth_rate: performanceData.shipment_growth_rate || 0,
        current_on_time_rate: performanceData.current_on_time_rate || 0,
        previous_on_time_rate: performanceData.previous_on_time_rate || 0,
        on_time_improvement: performanceData.on_time_improvement || 0,
      } : null;

      // Process revenue data - use exact API field names
      const revenue: DashboardRevenue | null = revenueData ? {
        total_revenue: revenueData.total_revenue || 0,
        average_shipment_value: revenueData.average_shipment_value || 0,
        daily_revenue: revenueData.daily_revenue || [],
        top_revenue_routes: revenueData.top_revenue_routes || [],
      } : null;

      // Process revenue chart data from daily_revenue
      let chartData: ChartData = {
        labels: [],
        datasets: []
      };
      if (revenue && revenue.daily_revenue && revenue.daily_revenue.length > 0) {
        chartData = {
          labels: revenue.daily_revenue.map(item => new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
          datasets: [{
            label: 'Daily Revenue',
            data: revenue.daily_revenue.map(item => item.revenue),
            borderColor: '#3b82f6',
            backgroundColor: '#3b82f6',
            fill: false,
          }]
        };
      }

      // Process recent activity data - it's already in the correct format
      const recentShipments: Shipment[] = [];

      // Update statistics using correct field names
      const statistics = {
        total: metrics?.total_shipments || 0,
        active: metrics?.in_transit_shipments || 0,
        delivered: metrics?.delivered_shipments || 0,
        delayed: metrics?.delayed_shipments || 0,
      };

      set({ 
        metrics,
        performance,
        revenue,
        chartData,
        recentShipments,
        statistics,
        recentActivity: Array.isArray(recentActivityData) ? recentActivityData : [],
        isLoading: false,
        error: null
      });

      // Still fetch organizations and users for compatibility
      await Promise.all([
        get().fetchOrganizations(apiClient),
        get().fetchUsers(apiClient),
      ]);
      
    } catch (error: unknown) {
      console.error("Error fetching dashboard data:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to fetch dashboard data. Please try again later.";
      set({ 
        error: errorMessage, 
        isLoading: false 
      });
    }
  },
  
  fetchShipments: async (apiClient: PaceAPIClient) => {
    if (!apiClient) {
      set({ error: "API client is not available for fetching shipments.", isLoading: false });
      return;
    }
    
    try {
      console.log("🚢 Fetching shipments from status search API...");
      
      // Use the new status search endpoint that includes tracking updates
      const response = await apiClient.getStatusSearch();
      console.log("📦 Raw status search response:", response);
      
      // Transform StatusSearchShipment[] to Shipment[] format for compatibility
      const statusShipments: StatusSearchShipment[] = Array.isArray(response) ? response : (Array.isArray(response.data) ? response.data : []);
      
      // Helper function to safely convert string role to UserRole enum
      const convertToUserRole = (role: string): UserRole => {
        const validRoles: UserRole[] = ["ADMIN", "BROKER", "CARRIER", "CONSIGNOR", "CONSIGNEE"];
        const upperRole = role.toUpperCase() as UserRole;
        return validRoles.includes(upperRole) ? upperRole : "CONSIGNOR";
      };
      
      // Helper function to safely convert commodity string to ShipmentType enum
      const convertToShipmentType = (commodity?: string): ShipmentType => {
        if (!commodity) return "general";
        const lowerCommodity = commodity.toLowerCase();
        const validTypes: ShipmentType[] = ["pharmaceutical", "electronics", "automotive", "general"];
        
        // Map common commodity names to shipment types
        if (lowerCommodity.includes("pharma") || lowerCommodity.includes("medicine") || lowerCommodity.includes("drug")) {
          return "pharmaceutical";
        }
        if (lowerCommodity.includes("electronic") || lowerCommodity.includes("computer") || lowerCommodity.includes("tech")) {
          return "electronics";
        }
        if (lowerCommodity.includes("auto") || lowerCommodity.includes("car") || lowerCommodity.includes("vehicle")) {
          return "automotive";
        }
        
        // Check if the commodity exactly matches a valid type
        if (validTypes.includes(lowerCommodity as ShipmentType)) {
          return lowerCommodity as ShipmentType;
        }
        
        return "general";
      };
      
      const shipments: Shipment[] = statusShipments.map((statusShipment: StatusSearchShipment) => ({
        id: statusShipment.id,
        tracking_number: statusShipment.tracking_number,
        external_reference_number: statusShipment.external_reference_number,
        status: statusShipment.status,
        origin_address: statusShipment.origin_address,
        destination_address: statusShipment.destination_address,
        expected_delivery_date: statusShipment.expected_delivery_date,
        actual_delivery_date: statusShipment.actual_delivery_date || undefined,
        created_at: statusShipment.created_at,
        updated_at: statusShipment.updated_at,
        consignor: statusShipment.consignor ? {
          ...statusShipment.consignor,
          role: convertToUserRole(statusShipment.consignor.role)
        } : undefined,
        consignee: statusShipment.consignee ? {
          ...statusShipment.consignee,
          role: convertToUserRole(statusShipment.consignee.role)
        } : undefined,
        carrier: statusShipment.carrier ? {
          ...statusShipment.carrier,
          role: convertToUserRole(statusShipment.carrier.role)
        } : undefined,
        shipment_details: statusShipment.shipment_details || undefined,
        // Add additional fields from StatusSearchShipment if needed
        carrier_id: statusShipment.carrier?.id,
        consignor_id: statusShipment.consignor?.id,
        consignee_id: statusShipment.consignee?.id,
        // Set shipment_type based on shipment_details
        shipment_type: convertToShipmentType(statusShipment.shipment_details?.commodity)
      }));
      
      console.log(`✅ Fetched ${shipments.length} shipments from status search`);
      
      const active = shipments.filter((s: Shipment) => s && s.status && s.status.toLowerCase() === 'in transit').length;
      const delivered = shipments.filter((s: Shipment) => s && s.status && s.status.toLowerCase() === 'delivered').length;
      const delayed = shipments.filter((s: Shipment) => 
        s && s.status && ['delayed', 'exception'].includes(s.status.toLowerCase())
      ).length;
      
      const today = new Date();
      const last6Months = Array.from({ length: 6 }, (_, i) => {
        const d = new Date(today);
        d.setMonth(d.getMonth() - i);
        return d;
      }).reverse();
      
      const monthLabels = last6Months.map(date => 
        date.toLocaleString('default', { month: 'short' })
      );
      
      const shipmentsByMonth: Record<string, number> = {};
      monthLabels.forEach(month => shipmentsByMonth[month] = 0);
      
      shipments.forEach((shipment: Shipment) => {
        if (shipment && shipment.created_at) {
          try {
            const shipmentDate = new Date(shipment.created_at);
            if (!isNaN(shipmentDate.getTime())) {
              const monthKey = shipmentDate.toLocaleString('default', { month: 'short' });
              if (shipmentsByMonth[monthKey] !== undefined) {
                shipmentsByMonth[monthKey]++;
              }
            }
          } catch {
            console.warn('Invalid date format for shipment:', shipment.id);
          }
        }
      });
      
      const recentShipments = [...shipments]
        .filter(s => s && s.created_at)
        .sort((a, b) => {
          try {
            const dateA = new Date(a.created_at);
            const dateB = new Date(b.created_at);
            if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) return 0;
            return dateB.getTime() - dateA.getTime();
          } catch {
            return 0;
          }
        })
        .slice(0, 10);
      
      set({
        recentShipments,
        statistics: {
          total: shipments.length,
          active,
          delivered,
          delayed
        },
        chartData: {
          labels: monthLabels,
          datasets: [
            {
              label: 'Shipments',
              data: monthLabels.map(month => shipmentsByMonth[month] || 0),
              borderColor: 'hsl(var(--primary))',
              backgroundColor: 'hsl(var(--primary) / 0.1)',
              fill: true,
            }
          ],
        },
        error: null,
      });
    } catch (error: unknown) {
      console.error("❌ Failed to fetch shipments:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set({ 
        error: `Failed to fetch shipments: ${errorMessage}`,
        isLoading: false 
      });
    }
  },

  fetchOrganizations: async (apiClient: PaceAPIClient) => {
    if (!apiClient) {
      set({ error: "API client is not available for fetching organizations." });
      return;
    }
    try {
      const response = await apiClient.get('/organization/', true); // With JWT authentication
      const organizations: Organization[] = Array.isArray(response) ? response : (Array.isArray(response.data) ? response.data : []);
      set({ organizations, error: null });
    } catch (error: unknown) {
      console.error("Error fetching organizations:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set({ 
        error: `Failed to fetch organizations: ${errorMessage}`,
        organizations: [] 
      });
    }
  },

  fetchUsers: async (apiClient: PaceAPIClient) => {
    if (!apiClient) {
      set({ error: "API client is not available for fetching users." });
      return;
    }
    try {
      // Fetch users with JWT authentication
      const response = await apiClient.get('/user/', true);
      
      const users: User[] = Array.isArray(response) ? response : (Array.isArray(response.data) ? response.data : []);
      set({ users, error: null });
    } catch (error: unknown) {
      console.error("Error fetching users:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set({ 
        error: `Failed to fetch users: ${errorMessage}`,
        users: [] 
      });
    }
  },

  fetchTopCarriers: async (apiClient: PaceAPIClient) => {
    if (!apiClient) {
      set({ error: "API client is not available for fetching top carriers." });
      return;
    }
    try {
      const response = await apiClient.get('/dashboard/top-carriers?limit=5&days=30', true);
      const topCarriers: TopCarrier[] = Array.isArray(response) ? response : (Array.isArray(response.data) ? response.data : []);
      set({ topCarriers, error: null });
    } catch (error: unknown) {
      console.error("Error fetching top carriers:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set({ 
        error: `Failed to fetch top carriers: ${errorMessage}`,
        topCarriers: [] 
      });
    }
  },

  fetchPerformanceData: async (apiClient: PaceAPIClient) => {
    if (!apiClient) {
      set({ error: "API client is not available for fetching performance data." });
      return;
    }
    try {
      const response = await apiClient.get('/dashboard/performance?days=10', true);
      const performanceData: PerformanceData = response.data || response;
      set({ performanceData, error: null });
    } catch (error: unknown) {
      console.error("Error fetching performance data:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set({ 
        error: `Failed to fetch performance data: ${errorMessage}`,
        performanceData: null 
      });
    }
  },

  fetchTrackingDetails: async (apiClient: PaceAPIClient, trackingNumber: string): Promise<TrackingDetails | null> => {
    if (!apiClient) {
      set({ error: "API client is not available for fetching tracking details." });
      return null;
    }
    try {
      console.log(`🔍 Fetching tracking details for: ${trackingNumber}`);
      
      // Use the new getShipmentTrackingDetails API method
      const response = await apiClient.getShipmentTrackingDetails(trackingNumber);
      console.log("📋 Tracking details response:", response);
      
      // Transform the response to match the expected TrackingDetails format
      if (response && response.tracking_updates) {
        interface TrackingUpdate {
          timestamp?: string;
          event_timestamp?: string;
          location?: string;
          status_description?: string;
          notes?: string;
          event_code?: string;
        }

        const trackingDetails: TrackingDetails = {
          tracking_number: response.tracking_number,
          carrier: response.carrier?.full_name || response.carrier?.email || 'Unknown Carrier',
          status: response.status,
          origin: response.origin_address || 'Unknown Origin',
          destination: response.destination_address || 'Unknown Destination',
          estimated_delivery: response.expected_delivery_date,
          updates: response.tracking_updates.map((update: TrackingUpdate) => ({
            timestamp: update.timestamp || update.event_timestamp || new Date().toISOString(),
            location: update.location || 'Unknown Location',
            status: update.status_description || 'Status Update',
            details: update.notes || update.event_code || 'No additional details'
          }))
        };
        
        console.log("✅ Transformed tracking details:", trackingDetails);
        return trackingDetails;
      }
      
      console.warn("⚠️ No tracking updates found in response");
      return null;
    } catch (error: unknown) {
      console.error("❌ Error fetching tracking details:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set({ 
        error: `Failed to fetch tracking details: ${errorMessage}`
      });
      return null;
    }
  },

  fetchQuotes: async (apiClient: PaceAPIClient) => {
    if (!apiClient) {
      set({ error: "API client is not available for fetching quotes." });
      return;
    }
    try {
      const response = await apiClient.get('/quotes/', true);
      const quotes: unknown[] = Array.isArray(response) ? response : (Array.isArray(response.data) ? response.data : []);
      set({ quotes, error: null });
    } catch (error: unknown) {
      console.error("Error fetching quotes:", error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set({ 
        error: `Failed to fetch quotes: ${errorMessage}`,
        quotes: [] 
      });
    }
  }
}));
