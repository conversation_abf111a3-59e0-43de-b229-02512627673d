export class PaceAPIClient {
  constructor(
    public orgId: string | null,
    public baseURL: string = process.env.NEXT_PUBLIC_API_URL || 'https://api.gopace.app',
    public useProxy: boolean = process.env.NODE_ENV === 'development'
  ) {}

  getHeaders(includeAuth = true): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    // Add authentication token (no PIN required anymore)
    if (includeAuth) {
      const token = localStorage.getItem('pace-token');
      if (token) {
        try {
          const parsedToken = JSON.parse(token);
          headers['Authorization'] = `Bearer ${parsedToken.access_token}`;
        } catch (error) {
          console.warn('Failed to parse stored token:', error);
        }
      }
    }
    
    return headers;
  }

  async get(endpoint: string, includeAuth = true) {
    try {
      let url: string;
      
      if (this.useProxy) {
        // Use local proxy endpoints in development for supported routes
        if (endpoint.startsWith('api/v1/')) {
          const cleanEndpoint = endpoint.replace('api/v1/', '');
          
          // Check if we have a Next.js API route for this endpoint
          const supportedProxyRoutes = [
            'dashboard/metrics',
            'dashboard/revenue', 
            'dashboard/performance',
            'dashboard/recent-activity',
            'shipment',
            'auth/login',
            'organization',
            'tracking/enhanced',
            'tracking'
          ];
          
          const hasProxyRoute = supportedProxyRoutes.some(route => 
            cleanEndpoint.startsWith(route)
          );
          
          if (hasProxyRoute) {
            url = `/api/${cleanEndpoint}`;
          } else {
            // Fall back to direct API call for unsupported routes
            url = `${this.baseURL}/${endpoint}`;
          }
        } else {
          // For other endpoints, use as-is with /api/ prefix
          const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
          url = `/api/${cleanEndpoint}`;
        }
      } else {
        // Use direct API calls in production
        const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
        url = `${this.baseURL}/${cleanEndpoint}`;
      }
      
      const response = await fetch(url, {
        headers: this.getHeaders(includeAuth),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  async post(endpoint: string, data: Record<string, unknown>, includeAuth = true) {
    try {
      let url: string;
      
      if (this.useProxy) {
        // Use local proxy endpoints in development for supported routes
        if (endpoint.startsWith('api/v1/')) {
          const cleanEndpoint = endpoint.replace('api/v1/', '');
          
          // Check if we have a Next.js API route for this endpoint
          const supportedProxyRoutes = [
            'dashboard/metrics',
            'dashboard/revenue', 
            'dashboard/performance',
            'dashboard/recent-activity',
            'shipment',
            'auth/login',
            'organization',
            'tracking/enhanced',
            'tracking'
          ];
          
          const hasProxyRoute = supportedProxyRoutes.some(route => 
            cleanEndpoint.startsWith(route)
          );
          
          if (hasProxyRoute) {
            url = `/api/${cleanEndpoint}`;
          } else {
            // Fall back to direct API call for unsupported routes
            url = `${this.baseURL}/${endpoint}`;
          }
        } else {
          // For other endpoints, use as-is with /api/ prefix
          const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
          url = `/api/${cleanEndpoint}`;
        }
      } else {
        // Use direct API calls in production
        const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
        url = `${this.baseURL}/${cleanEndpoint}`;
      }
      
      const response = await fetch(url, {
        method: 'POST',
        headers: this.getHeaders(includeAuth),
        body: JSON.stringify(data),
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return response.json();
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  }

  // Authentication
  async login(username: string, password: string) {
    return this.post('api/v1/auth/login', { username, password }, false);
  }

  // Organizations
  async getOrganizations() {
    return this.get('api/v1/organization/');
  }

  // Dashboard APIs
  async getDashboardMetrics(days: number = 30) {
    return this.get(`api/v1/dashboard/metrics?days=${days}`);
  }

  async getDashboardRevenue(days: number = 30) {
    return this.get(`api/v1/dashboard/revenue?days=${days}`);
  }

  async getDashboardPerformance(days: number = 30) {
    return this.get(`api/v1/dashboard/performance?days=${days}`);
  }

  async getRecentActivity(limit: number = 10) {
    return this.get(`api/v1/dashboard/recent-activity?limit=${limit}`);
  }

  // Shipments APIs
  async getShipments(limit?: number, offset?: number) {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (offset) params.append('offset', offset.toString());
    const query = params.toString() ? `?${params.toString()}` : '';
    return this.get(`api/v1/shipment/${query}`);
  }

  async getShipment(shipmentId: string) {
    return this.get(`api/v1/shipment/${shipmentId}`);
  }

  // Enhanced Tracking APIs
  async getEnhancedTracking(trackingNumber: string) {
    return this.get(`api/v1/tracking/enhanced/${trackingNumber}`);
  }

  async getBasicTracking(trackingNumber: string) {
    return this.get(`api/v1/tracking/${trackingNumber}`);
  }

  async getTrackingByQuery(trackingNumber: string) {
    return this.get(`api/v1/tracking/?tracking_number=${trackingNumber}`);
  }

  // Status search API - gets all shipments with status updates
  async getStatusSearch() {
    return this.get('api/v1/status/search');
  }

  // Shipment tracking details by tracking number
  async getShipmentTrackingDetails(trackingNumber: string) {
    return this.get(`api/v1/tracking/${trackingNumber}`);
  }

  // Legacy methods for backward compatibility
  async getEnhancedShipments() {
    return this.get('/api/shipments/enhanced');
  }

  async processVoiceCommand(text: string, context?: Record<string, unknown>) {
    return this.post('/api/voice/process', {
      text,
      context: context || {},
    });
  }

  async getVoiceEnhancedTracking(trackingNumber: string, enableVoice = false) {
    const endpoint = `/api/tracking/${trackingNumber}${enableVoice ? '?voice=true' : ''}`;
    return this.get(endpoint);
  }
}
