"use client";

import { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { AuthenticatedUser, JWTToken, UserRole } from '@/lib/types';
import LogRocket from 'logrocket';

// Define the type of data stored in the context
interface AuthContextType {
  user: AuthenticatedUser | null;
  token: JWTToken | null;
  isLoading: boolean;
  login: (email: string, password?: string) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  isAuthenticated: boolean;
  hasRole: (role: UserRole) => boolean;
  hasPermission: (permission: string) => boolean;
  canAccessWithoutPIN: (operation: string) => boolean;
}

// Create the AuthContext
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Create a custom hook to use the AuthContext
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

// Create the AuthProvider component
export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthenticatedUser | null>(null);
  const [token, setToken] = useState<JWTToken | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  // Refresh token function
  const refreshToken = useCallback(async () => {
    try {
      const storedToken = localStorage.getItem('pace-token');
      if (!storedToken) throw new Error('No refresh token available');

      const parsedToken = JSON.parse(storedToken);
      if (!parsedToken.refresh_token) throw new Error('No refresh token available');

      // Make real API call to refresh token
      const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
      
      const response = await fetch(`${API_URL}/api/v1/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          refresh_token: parsedToken.refresh_token
        })
      });

      if (!response.ok) {
        throw new Error('Token refresh failed');
      }

      const refreshData = await response.json();
      
      const newToken: JWTToken = {
        access_token: refreshData.access_token,
        token_type: refreshData.token_type || 'Bearer',
        expires_in: Date.now() + (refreshData.expires_in || 8 * 60 * 60) * 1000, // Convert to timestamp
        refresh_token: refreshData.refresh_token || parsedToken.refresh_token // Keep old refresh token if new one not provided
      };

      setToken(newToken);
      localStorage.setItem('pace-token', JSON.stringify(newToken));
      localStorage.setItem('jwtToken', JSON.stringify(newToken)); // Also update legacy format
    } catch (error) {
      console.error('Token refresh failed:', error);
      // Clear invalid session state
      setUser(null);
      setToken(null);
      localStorage.removeItem('pace-token');
      localStorage.removeItem('pace-user');
      localStorage.removeItem('jwtToken');
      localStorage.removeItem('currentUser');
      router.push('/');
    }
  }, [router]);

  // Check for existing session on mount
  useEffect(() => {
    const checkSession = async () => {
      try {
        const storedToken = localStorage.getItem('pace-token');
        const storedUser = localStorage.getItem('pace-user');
        
        if (storedToken && storedUser) {
          const parsedToken = JSON.parse(storedToken);
          const parsedUser = JSON.parse(storedUser);
          
          // Immediately set user and token to avoid redirect during verification
          setToken(parsedToken);
          setUser(parsedUser);
          
          // Sync with cookies for middleware access
          document.cookie = `pace-token=${JSON.stringify(parsedToken)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
          
          // Check if token is still valid (basic time check)
          const now = Date.now();
          const tokenExpiry = parsedToken.expires_in;
          
          if (now < tokenExpiry) {
            // Token appears valid, but verify with backend in background
            try {
              const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
              const response = await fetch(`${API_URL}/api/v1/auth/me`, {
                headers: {
                  'Authorization': `Bearer ${parsedToken.access_token}`,
                  'Content-Type': 'application/json'
                }
              });
              
              if (response.ok) {
                // Token is valid, update legacy localStorage for API compatibility
                localStorage.setItem('jwtToken', JSON.stringify(parsedToken));
                localStorage.setItem('currentUser', JSON.stringify(parsedUser));
              } else {
                // Token invalid, try to refresh
                if (parsedToken.refresh_token) {
                  await refreshToken();
                } else {
                  // Clear invalid session
                  setUser(null);
                  setToken(null);
                  localStorage.removeItem('pace-token');
                  localStorage.removeItem('pace-user');
                  localStorage.removeItem('jwtToken');
                  localStorage.removeItem('currentUser');
                }
              }
            } catch (verificationError) {
              console.warn('Token verification failed:', verificationError);
              // Try refresh if available
              if (parsedToken.refresh_token) {
                await refreshToken();
              } else {
                // Clear invalid session
                setUser(null);
                setToken(null);
                localStorage.removeItem('pace-token');
                localStorage.removeItem('pace-user');
                localStorage.removeItem('jwtToken');
                localStorage.removeItem('currentUser');
              }
            }
          } else {
            // Token expired, try to refresh if refresh token exists
            if (parsedToken.refresh_token) {
              await refreshToken();
            } else {
              // Clear expired session
              setUser(null);
              setToken(null);
              localStorage.removeItem('pace-token');
              localStorage.removeItem('pace-user');
              localStorage.removeItem('jwtToken');
              localStorage.removeItem('currentUser');
            }
          }
        }
      } catch (error) {
        console.error('Error checking session:', error);
        // Clear invalid stored data
        localStorage.removeItem('pace-token');
        localStorage.removeItem('pace-user');
        localStorage.removeItem('jwtToken');
        localStorage.removeItem('currentUser');
      }
      
      setIsLoading(false);
    };

    checkSession();
  }, [refreshToken, router]);

  // Login function with JWT authentication
  const login = async (email: string, password?: string) => {
    setIsLoading(true);
    
    try {
      // Make real API request to authenticate with backend
      const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
      
      // Log login attempt (without password) - only in production
      if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
        LogRocket.track('Login Attempt', { email });
      }
      
      // Call the backend authentication endpoint with form-encoded data
      const formData = new URLSearchParams();
      formData.append('username', email);
      formData.append('password', password || 'default-password');

      const response = await fetch('https://api.gopace.app/api/v1/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: formData
      });

      if (!response.ok) {
        // Track failed login attempts in LogRocket (without exposing password) - only in production
        if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
          LogRocket.track('Login Failed', { 
            email,
            statusCode: response.status,
            reason: 'Authentication failed'
          });
        }
        throw new Error('Authentication failed');
      }

      const authData = await response.json();
      
      // Extract JWT token from response
      const jwtToken: JWTToken = {
        access_token: authData.access_token,
        token_type: authData.token_type || 'Bearer',
        expires_in: Date.now() + (authData.expires_in || 8 * 60 * 60) * 1000, // Convert to timestamp
        refresh_token: authData.refresh_token
      };

      // Get user information from the token or make a separate API call
      let authenticatedUser: AuthenticatedUser;
      
      try {
        // Try to get current user info from backend
        const userResponse = await fetch(`${API_URL}/api/v1/auth/me`, {
          headers: {
            'Authorization': `Bearer ${jwtToken.access_token}`,
            'Content-Type': 'application/json'
          }
        });
        
        if (userResponse.ok) {
          const userData = await userResponse.json();
          authenticatedUser = {
            id: userData.id,
            email: userData.email,
            full_name: userData.full_name || userData.name,
            role: userData.role as UserRole,
            organization_id: userData.organization_id,
            permissions: getRolePermissions(userData.role as UserRole),
            created_at: userData.created_at,
            updated_at: userData.updated_at,
            last_login: new Date().toISOString(),
            is_active: userData.is_active !== false
          };
        } else {
          throw new Error('Failed to get user info');
        }
      } catch (userError) {
        // Fallback: create user object from email if backend call fails
        console.warn('Failed to get user from backend, using fallback:', userError);
        
        // Determine role based on email domain or other logic (fallback only)
        let role: UserRole = "CONSIGNEE"; // Default role
        if (email.includes('admin')) role = "ADMIN";
        else if (email.includes('broker')) role = "BROKER";
        else if (email.includes('carrier')) role = "CARRIER";
        else if (email.includes('consignor')) role = "CONSIGNOR";

        authenticatedUser = {
          id: parseInt(`${Date.now()}`.slice(-6)),
          email: email,
          full_name: email.split('@')[0].replace(/[._]/g, ' ').split(' ').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' '),
          role: role,
          permissions: getRolePermissions(role),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_login: new Date().toISOString(),
          is_active: true
        };
      }
      
      // Set the user and token in state
      setUser(authenticatedUser);
      setToken(jwtToken);
      
      // Track successful login in LogRocket - only in production
      if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
        LogRocket.track('Login Success', { 
          userId: authenticatedUser.id, 
          role: authenticatedUser.role 
        });
        
        // Identify the user in LogRocket for session tracking
        LogRocket.identify(String(authenticatedUser.id), {
          name: authenticatedUser.full_name || authenticatedUser.email.split('@')[0],
          email: authenticatedUser.email,
          role: authenticatedUser.role
        });
      }
      
      // Store in localStorage for persistent login
      localStorage.setItem('pace-token', JSON.stringify(jwtToken));
      localStorage.setItem('pace-user', JSON.stringify(authenticatedUser));
      
      // Also store in cookies for middleware access
      document.cookie = `pace-token=${JSON.stringify(jwtToken)}; path=/; max-age=${7 * 24 * 60 * 60}; SameSite=Strict`;
      
      // Also store in the format expected by the legacy API client
      localStorage.setItem('jwtToken', JSON.stringify(jwtToken));
      localStorage.setItem('currentUser', JSON.stringify(authenticatedUser));
      
      // Check for redirect parameter from URL
      const urlParams = new URLSearchParams(window.location.search);
      const redirectTo = urlParams.get('redirect') || '/dashboard';
      
      // Redirect to intended destination or dashboard
      router.push(redirectTo);
    } catch (error) {
      console.error('Login error:', error);
      // Track login errors in LogRocket - only in production
      if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
        LogRocket.track('Login Error', { 
          email,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
      throw new Error('Invalid credentials');
    } finally {
      setIsLoading(false);
    }
  };

  // Logout function
  const logout = useCallback(async () => {
    try {
      // Attempt to call backend logout endpoint
      const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
      const storedToken = localStorage.getItem('pace-token');
      
      if (storedToken) {
        try {
          const parsedToken = JSON.parse(storedToken);
          await fetch(`${API_URL}/api/v1/auth/logout`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${parsedToken.access_token}`,
              'Content-Type': 'application/json',
            }
          });
        } catch (logoutError) {
          console.warn('Backend logout failed, proceeding with local cleanup:', logoutError);
        }
      }
    } catch (error) {
      console.warn('Error during logout:', error);
    } finally {
      // Track logout event in LogRocket - only in production
      if (typeof window !== 'undefined' && user && process.env.NODE_ENV === 'production') {
        LogRocket.track('User Logout', {
          userId: user.id,
          email: user.email,
          role: user.role
        });
      }
      
      // Always clean up local state regardless of backend response
      setUser(null);
      setToken(null);
      
      // Remove all authentication-related localStorage items
      localStorage.removeItem('pace-user');
      localStorage.removeItem('pace-token');
      localStorage.removeItem('organizationId');
      localStorage.removeItem('organizationPin');
      localStorage.removeItem('organizationName');
      localStorage.removeItem('selectedOrganizationId');
      localStorage.removeItem('selectedOrganizationName');
      
      // Clear auth cookie for middleware
      document.cookie = 'pace-token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Strict';
      
      // Redirect to home page
      router.push('/');
    }
  }, [router, user]);

  // Role checking functions
  const hasRole = (role: UserRole): boolean => {
    return user?.role === role;
  };

  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false;
  };

  // Check if user can access operation without PIN (80% reduction in PIN prompts)
  const canAccessWithoutPIN = (operation: string): boolean => {
    if (!user) return false;
    
    // ADMIN role bypasses most PIN requirements
    if (user.role === 'ADMIN' && hasPermission('bypass_pin_requirements')) {
      return !['delete_shipment', 'manage_system_settings', 'delete_organization'].includes(operation);
    }
    
    // Viewing operations generally don't require PIN (80% reduction)
    const viewingOperations = [
      'view_dashboard', 'view_shipments', 'view_tracking', 'view_reports',
      'view_organizations', 'view_users', 'browse_data'
    ];
    
    if (viewingOperations.includes(operation)) {
      return true;
    }
    
    // Voice operations can be PIN-exempt for machine-to-machine authentication
    if (operation === 'voice_operations' && hasPermission('voice_operations')) {
      return true;
    }
    
    return false;
  };

  // Get permissions based on role
  const getRolePermissions = (role: UserRole): string[] => {
    switch (role) {
      case 'ADMIN':
        return [
          'view_all_shipments', 'create_shipment', 'edit_shipment', 'delete_shipment',
          'view_all_organizations', 'manage_organizations', 'manage_users',
          'view_reports', 'manage_system_settings', 'voice_operations',
          'bypass_pin_requirements'
        ];
      case 'BROKER':
        return [
          'view_org_shipments', 'create_shipment', 'edit_shipment',
          'view_tracking', 'create_tracking_updates', 'view_reports',
          'voice_operations'
        ];
      case 'CARRIER':
        return [
          'view_assigned_shipments', 'update_shipment_status', 'create_tracking_updates',
          'view_tracking', 'voice_operations'
        ];
      case 'CONSIGNOR':
        return [
          'view_own_shipments', 'create_shipment', 'view_tracking', 'voice_operations'
        ];
      case 'CONSIGNEE':
        return [
          'view_incoming_shipments', 'view_tracking', 'confirm_delivery'
        ];
      default:
        return ['view_tracking'];
    }
  };

  // Value to be provided to consumers of this context
  const value = {
    user,
    token,
    isLoading,
    login,
    logout,
    refreshToken,
    isAuthenticated: !!user && !!token,
    hasRole,
    hasPermission,
    canAccessWithoutPIN
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}
