"use client";

import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { Organization } from '@/lib/types';
import { PaceAPIClient } from '@/lib/api-client';

interface OrganizationContextType {
  organizationId: string | null;
  setOrganizationContext: (id: string | null) => void;
  apiClient: PaceAPIClient | null;
  organizations: Organization[];
  fetchAllOrganizations: () => Promise<void>;
  isLoadingOrganizations: boolean;
  setSelectedOrganization: (org: Organization) => void;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: ReactNode }) {
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoadingOrganizations, setIsLoadingOrganizations] = useState(false);
  const [apiClient, setApiClient] = useState<PaceAPIClient | null>(null);

  const setContext = useCallback((id: string | null) => {
    setOrganizationId(id);
    
    if (id) {
      localStorage.setItem('organizationId', id);
    } else {
      localStorage.removeItem('organizationId');
      localStorage.removeItem('organizationName');
      localStorage.removeItem('selectedOrganizationId');
      localStorage.removeItem('selectedOrganizationName');
    }
    
    // Create API client with JWT authentication (no PIN required)
    const newApiClient = new PaceAPIClient(id);
    setApiClient(newApiClient);
  }, []);

  useEffect(() => {
    const storedOrgId = localStorage.getItem('organizationId');
    setContext(storedOrgId);
  }, [setContext]);

  const fetchAllOrganizations = useCallback(async () => {
    setIsLoadingOrganizations(true);
    try {
      const client = new PaceAPIClient(null);
      const response = await client.getOrganizations();
      if (Array.isArray(response)) {
        setOrganizations(response);
      } else if (response.data && Array.isArray(response.data)) {
        setOrganizations(response.data);
      } else {
        setOrganizations([]);
      }
    } catch (error) {
      console.error('Failed to fetch organizations:', error);
      setOrganizations([]);
    } finally {
      setIsLoadingOrganizations(false);
    }
  }, []);

  const setSelectedOrganization = useCallback((org: Organization) => {
    localStorage.setItem('selectedOrganizationId', org.id.toString());
    localStorage.setItem('selectedOrganizationName', org.name || "Organization");
    
    const foundOrg = organizations.find(o => o.id.toString() === org.id.toString());
    if (foundOrg) {
      localStorage.setItem('organizationName', foundOrg.name || "Organization");
    }
  }, [organizations]);

  return (
    <OrganizationContext.Provider value={{
      organizationId,
      setOrganizationContext: setContext,
      apiClient,
      organizations,
      fetchAllOrganizations,
      isLoadingOrganizations,
      setSelectedOrganization
    }}>
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganizationContext() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error('useOrganizationContext must be used within an OrganizationProvider');
  }
  return context;
}
