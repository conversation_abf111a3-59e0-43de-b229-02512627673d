# VAPI Voice Intelligence Integration Guide

## 🎯 Overview

The PACE dashboard now includes comprehensive VAPI integration for voice-powered freight operations. Users can create AI assistants, manage voice calls, configure webhooks, and handle voice commands through an intuitive UI.

## 📍 Dashboard Access

Navigate to **Dashboard → Voice Intelligence** (accessible to ADMIN and BROKER roles)

## 🚀 Quick Setup

### 1. Configure VAPI Credentials

1. Go to **Voice Intelligence → Settings** tab
2. Add your VAPI API key from [vapi.ai](https://vapi.ai)
3. Configure webhook secret (auto-generated)
4. Save settings

### 2. Create Your First Assistant

1. Go to **Voice Intelligence → Assistants** tab
2. Click **"Quick Setup"** - this automatically:
   - Creates an assistant with smart defaults
   - Configures functions based on your role (ADMIN, BROKER, CARRIER, etc.)
   - Sets up OpenAI GPT-4 with natural voice
   - Links to your webhook endpoints
3. Done! Your assistant is ready to use

### 3. Set Up Webhooks in VAPI Dashboard

Copy these URLs from the **Webhooks** tab:

```
Webhook Events: http://localhost:3001/api/webhooks/vapi/events
Server Tools:   http://localhost:3001/api/vapi-tools
```

In your VAPI dashboard:
1. Add the webhook URL to receive call events
2. Configure your assistant to use the Server Tools URL
3. Set webhook secret for security

## 🔧 Auto-Configured Functions by Role

Functions are automatically added based on your user role:

### 👤 **All Users**
- `get_shipment_status` - Track any shipment by voice

### 🔧 **BROKER & ADMIN**
- `get_shipment_status` - Real-time tracking
- `create_shipment_alert` - Create delay/damage alerts
- `notify_stakeholders` - Send notifications to relevant parties

### 👑 **ADMIN Only**
- All BROKER functions plus:
- `generate_report` - Create operational reports by voice

### 🚛 **CARRIER**
- `get_shipment_status` - Track assigned shipments
- `update_delivery_status` - Update delivery progress

### 📦 **CONSIGNOR/CONSIGNEE**
- `get_shipment_status` - Track your shipments only

> **💡 Smart Auto-Configuration**: No manual setup needed! Functions are tailored to your role and permissions automatically.

## 🎙️ Voice Command Examples

```
User: "Hi, can you check my shipment PACE123456?"
Assistant: "Your shipment PACE123456 is currently in transit in Chicago, IL. It's 75% complete and expected to be delivered on January 25th, 2024 via FedEx. Everything is on schedule with no delays reported."

User: "Create a delay alert for that shipment"
Assistant: "I've created a delay alert for shipment PACE123456. The relevant stakeholders will be notified about potential delays."

User: "Notify the warehouse and customer service"
Assistant: "I've successfully sent notifications about shipment PACE123456 to 2 stakeholders including the warehouse team and customer service."
```

## 📊 Features Available

### ✅ Assistant Management
- Create/edit/delete voice assistants
- Configure AI models (OpenAI, Anthropic)
- Set voice providers (OpenAI, ElevenLabs)
- Add custom functions for freight operations

### ✅ Call History
- View recent voice interactions
- Monitor call duration and costs
- Access call transcripts
- Track assistant performance

### ✅ Webhook Configuration
- Pre-configured webhook endpoints
- Event handling for call lifecycle
- Real-time transcript processing
- Function call monitoring

### ✅ Voice Settings
- Enable/disable voice commands
- Auto-transcription controls
- Default assistant selection
- Global voice feature toggles

## 🔗 API Endpoints

The integration provides these endpoints:

```typescript
// Function Tools
POST /api/vapi-tools/get-shipment-status
POST /api/vapi-tools/create-shipment-alert  
POST /api/vapi-tools/notify-stakeholders

// Webhook Events
POST /api/webhooks/vapi/events
GET  /api/webhooks/vapi/events (verification)
```

## 🛠️ Development URLs

For local development (automatically configured):

```
Webhook URL: http://localhost:3001/api/webhooks/vapi/events
Tools URL:   http://localhost:3001/api/vapi-tools
```

For production, update these to your deployed domain.

## 🎨 UI Components

### Voice Intelligence Dashboard
- **Location**: `/dashboard/voice`
- **Components**: 4 main tabs (Assistants, Calls, Webhooks, Settings)
- **Styling**: Matches the modern gradient design system
- **Responsive**: Mobile-friendly interface

### Assistant Form
- **Component**: `AssistantForm`
- **Purpose**: Create/edit voice assistants
- **Features**: Model selection, voice configuration, function management

### VAPI Service
- **File**: `lib/vapi-service.ts`
- **Purpose**: Handle VAPI API interactions
- **Features**: Assistant CRUD, call management, helper utilities

## 🔐 Security & Access Control

- **Role-based access**: ADMIN and BROKER roles only
- **PIN requirements**: Configurable for sensitive operations
- **API key security**: Masked input fields with show/hide toggle
- **Webhook validation**: HMAC signature verification (server-side)

## 📱 Mobile Responsiveness

The Voice Intelligence interface is fully responsive:
- Collapsible sidebar navigation
- Responsive grid layouts for assistants
- Mobile-optimized forms and inputs
- Touch-friendly buttons and controls

## 🎯 Next Steps

1. **Test the integration**: Create a test assistant and make a sample call
2. **Customize functions**: Add business-specific voice functions
3. **Monitor usage**: Track call history and assistant performance
4. **Scale up**: Create multiple assistants for different use cases

## 🆘 Support

For VAPI-specific issues:
- Check the **Call History** tab for transcript errors
- Verify webhook endpoints are accessible
- Ensure API keys are correctly configured
- Review console logs for debugging information

The integration is designed to be intuitive and self-service, matching the modern design aesthetic of the PACE platform.