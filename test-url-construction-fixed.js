// Test script to verify API endpoints are working correctly
console.log('Testing API endpoint construction...');

// Simulate the PaceAPIClient logic
const useProxy = true; // development mode

function testURL(endpoint) {
  let url;
  
  if (useProxy) {
    if (endpoint.startsWith('api/v1/')) {
      const cleanEndpoint = endpoint.replace('api/v1/', '');
      
      const supportedProxyRoutes = [
        'dashboard/metrics',
        'dashboard/revenue', 
        'dashboard/performance',
        'dashboard/recent-activity',
        'shipment',
        'auth/login',
        'organization',
        'tracking/enhanced',
        'tracking'
      ];
      
      const hasProxyRoute = supportedProxyRoutes.some(route => 
        cleanEndpoint.startsWith(route)
      );
      
      if (hasProxyRoute) {
        url = `/api/${cleanEndpoint}`;
      } else {
        url = `https://api.gopace.app/${endpoint}`;
      }
    } else {
      const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
      url = `/api/${cleanEndpoint}`;
    }
  }
  
  console.log(`${endpoint} -> ${url}`);
  return url;
}

// Test various endpoints
const testEndpoints = [
  'api/v1/dashboard/metrics',
  'api/v1/dashboard/revenue',
  'api/v1/auth/login',
  'api/v1/organization/',
  'api/v1/tracking/enhanced/123456',
  'api/v1/tracking/123456',
  'api/v1/tracking/?tracking_number=123456',
  'api/v1/shipment/',
  'api/v1/unknown/endpoint'
];

console.log('\n=== URL Construction Test ===');
testEndpoints.forEach(testURL);

console.log('\n=== Expected Results ===');
console.log('✓ Proxy routes should go to /api/...');
console.log('✓ Unsupported routes should go to https://api.gopace.app/...');
console.log('✓ No double /api/api/ should appear');
