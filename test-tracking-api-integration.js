#!/usr/bin/env node

/**
 * Comprehensive Test Script for New Tracking API Integration
 * This script tests all 4 new tracking endpoints integrated into the Fr8+ dashboard
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:4030/api/v1';
const TEST_ORG_ID = '17';
const TEST_ORG_PIN = '1234';

// Test data
const TEST_TRACKING_NUMBER = 'TRK123456789';
const TEST_BROKERAGE_ID = 'BRK001';
const TEST_TRACKING_EVENT = {
  status: 'In Transit',
  location: 'Los Angeles, CA',
  timestamp: new Date().toISOString(),
  description: 'Package picked up from origin facility'
};

const headers = {
  'Content-Type': 'application/json',
  'X-Organization-ID': TEST_ORG_ID,
  'X-Organization-PIN': TEST_ORG_PIN
};

async function testEndpoint(name, method, url, data = null) {
  console.log(`\n🧪 Testing: ${name}`);
  console.log(`📍 ${method.toUpperCase()} ${url}`);
  
  try {
    const config = {
      method,
      url: `${BASE_URL}${url}`,
      headers,
      ...(data && { data })
    };
    
    const response = await axios(config);
    console.log(`✅ Status: ${response.status}`);
    console.log(`📦 Response:`, JSON.stringify(response.data, null, 2));
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    console.log(`❌ Error: ${error.response?.status || 'Network Error'}`);
    console.log(`📄 Details:`, error.response?.data || error.message);
    return { success: false, status: error.response?.status, error: error.response?.data || error.message };
  }
}

async function runTrackingAPITests() {
  console.log('🚀 Starting Comprehensive Tracking API Integration Tests');
  console.log(`🏢 Organization ID: ${TEST_ORG_ID}`);
  console.log(`🔐 Organization PIN: ${TEST_ORG_PIN}`);
  console.log('='.repeat(80));

  const results = {};

  // Test 1: Get Tracking by Number
  results.getTrackingByNumber = await testEndpoint(
    'Get Tracking by Number',
    'GET',
    `/tracking/${TEST_TRACKING_NUMBER}`
  );

  // Test 2: Get Tracking by Brokerage ID
  results.getTrackingByBrokerageId = await testEndpoint(
    'Get Tracking by Brokerage ID',
    'GET',
    `/status/${TEST_BROKERAGE_ID}/tracking`
  );

  // Test 3: Add Tracking Event by Number
  results.addTrackingEventByNumber = await testEndpoint(
    'Add Tracking Event by Number',
    'POST',
    `/tracking/?tracking_number=${TEST_TRACKING_NUMBER}`,
    TEST_TRACKING_EVENT
  );

  // Test 4: Add Tracking Event to Brokerage
  results.addTrackingEventToBrokerage = await testEndpoint(
    'Add Tracking Event to Brokerage',
    'POST',
    `/status/${TEST_BROKERAGE_ID}/tracking`,
    TEST_TRACKING_EVENT
  );

  // Test 5: Verify existing endpoints still work
  console.log('\n🔍 Testing Existing Endpoints for Regression');
  
  results.healthCheck = await testEndpoint(
    'Health Check',
    'GET',
    '/health/'
  );

  results.shipmentSearch = await testEndpoint(
    'Shipment Search',
    'GET',
    '/status/search'
  );

  // Summary Report
  console.log('\n' + '='.repeat(80));
  console.log('📊 TRACKING API INTEGRATION TEST SUMMARY');
  console.log('='.repeat(80));

  const categories = {
    'New Tracking Endpoints': [
      'getTrackingByNumber',
      'getTrackingByBrokerageId',
      'addTrackingEventByNumber',
      'addTrackingEventToBrokerage'
    ],
    'Existing Endpoints (Regression Test)': [
      'healthCheck',
      'shipmentSearch'
    ]
  };

  Object.entries(categories).forEach(([category, tests]) => {
    console.log(`\n📋 ${category}:`);
    tests.forEach(test => {
      const result = results[test];
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      const statusCode = result.status ? ` (${result.status})` : '';
      console.log(`  ${status} ${test}${statusCode}`);
    });
  });

  // Integration Status
  const newEndpointsPassing = Object.values(results)
    .slice(0, 4)
    .filter(r => r.success).length;
  
  const regressionTestsPassing = Object.values(results)
    .slice(4)
    .filter(r => r.success).length;

  console.log('\n🎯 INTEGRATION STATUS:');
  console.log(`📊 New Tracking Endpoints: ${newEndpointsPassing}/4 passing`);
  console.log(`🔄 Regression Tests: ${regressionTestsPassing}/2 passing`);

  if (newEndpointsPassing === 0) {
    console.log('\n⏳ BACKEND STATUS: New tracking endpoints not yet deployed');
    console.log('💡 NEXT STEPS: Deploy backend with new tracking API endpoints');
  } else if (newEndpointsPassing < 4) {
    console.log('\n⚠️  PARTIAL DEPLOYMENT: Some new tracking endpoints are working');
    console.log('💡 NEXT STEPS: Complete backend deployment of remaining endpoints');
  } else {
    console.log('\n🎉 SUCCESS: All new tracking endpoints are working!');
    console.log('💡 NEXT STEPS: Proceed with full integration testing');
  }

  console.log('\n📚 DOCUMENTATION:');
  console.log('📖 Integration details: /INTEGRATION_STATUS.md');
  console.log('🔗 Frontend URL: http://localhost:3000');
  console.log('🔗 Backend URL: http://localhost:4030');

  return results;
}

// Error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the tests
if (require.main === module) {
  runTrackingAPITests()
    .then(() => {
      console.log('\n✨ Test suite completed!');
    })
    .catch(error => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { runTrackingAPITests };
