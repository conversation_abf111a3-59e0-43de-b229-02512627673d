#!/usr/bin/env node

// Test script to validate location extraction utility
// This helps us understand what fields are missing and causes "N/A" values

const { extractLocation, getDisplayLocation, debugLocationFields } = require('./src/lib/location-utils.ts');

console.log('🧪 Testing Location Extraction Utility\n');

// Test cases representing different possible API response formats
const testCases = [
  {
    name: "Direct location field",
    data: {
      id: 1,
      status_description: "Package picked up",
      location: "Lagos Port, Nigeria",
      timestamp: "2025-05-19T10:00:00Z"
    }
  },
  {
    name: "City and state fields",
    data: {
      id: 2,
      status_description: "In transit",
      city: "Nairobi",
      state: "Nairobi County",
      country: "Kenya",
      timestamp: "2025-05-20T14:00:00Z"
    }
  },
  {
    name: "Port name field",
    data: {
      id: 3,
      status_description: "Arrived at port",
      port_name: "Port of Mombasa",
      timestamp: "2025-05-21T08:00:00Z"
    }
  },
  {
    name: "Location in status description",
    data: {
      id: 4,
      status_description: "Package arrived at Lagos-Apapa Port Complex",
      timestamp: "2025-05-22T12:00:00Z"
    }
  },
  {
    name: "Location in notes",
    data: {
      id: 5,
      status_description: "Customs clearance",
      notes: "Cargo processing in Nigeria Customs, Apapa",
      timestamp: "2025-05-23T09:00:00Z"
    }
  },
  {
    name: "Facility name",
    data: {
      id: 6,
      status_description: "At warehouse",
      facility_name: "Central Distribution Hub",
      timestamp: "2025-05-24T16:00:00Z"
    }
  },
  {
    name: "No location data (should show smart fallback)",
    data: {
      id: 7,
      status_description: "In transit",
      timestamp: "2025-05-25T11:00:00Z"
    }
  },
  {
    name: "Current location field",
    data: {
      id: 8,
      status_description: "Shipment update",
      current_location: "Warehouse District, Lagos",
      timestamp: "2025-05-26T07:00:00Z"
    }
  }
];

// Run tests
testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.name}`);
  console.log('Input data:', JSON.stringify(testCase.data, null, 2));
  
  // Test extraction function
  const extraction = extractLocation(testCase.data);
  console.log('Extraction result:', extraction);
  
  // Test display function
  const displayLocation = getDisplayLocation(testCase.data);
  console.log('Display location:', displayLocation);
  
  console.log('---\n');
});

console.log('🔍 Field Analysis Summary:');
console.log('The location extraction utility checks for these field names:');
console.log('• High confidence: location, current_location, address, full_address, location_name, place_name');
console.log('• Medium confidence: city+state+country combinations, port_name, facility_name, terminal_name');
console.log('• Low confidence: extracted from status_description, notes, comments using patterns');
console.log('• Fallback: smart defaults based on status keywords');

console.log('\n💡 To fix "N/A" values, ensure your API provides data in one of these fields:');
console.log('1. location (preferred)');
console.log('2. current_location'); 
console.log('3. city + state/country');
console.log('4. port_name or facility_name');
console.log('5. Include location in status_description using "at [location]" pattern');
