# Authentication and Organization Selection Flow Test

## Steps to Test

1. **Visit Home Page**
   - Navigate to the application's home page
   - Verify that the landing page loads with login button

2. **Login Test**
   - Click the "Login" button
   - Enter email (any valid email format) and password
   - Submit the login form
   - Verify that you're redirected to the dashboard or organization selection

3. **Organization Selection Test**
   - After login, verify the user details are shown in the organization selector
   - Verify organizations are automatically loaded (mock data should appear)
   - Select an organization from the dropdown
   - Enter a PIN (any PIN will work in the mock system)
   - Click "Continue to Dashboard"

4. **Dashboard Test**
   - Verify you're redirected to the dashboard with the selected organization
   - Check that the organization name appears in the organization switcher
   - Check that the user name/email appears in the user account menu

5. **Organization Switch Test**
   - Click the organization switcher
   - Select "Switch Organization"
   - Verify you're presented with the organization selection screen again
   - Select a different organization and continue to dashboard

6. **Logout Test**
   - Click on the user account menu
   - Select "Logout"
   - Verify you're redirected to the home/landing page
   - Try to access the dashboard directly by navigating to /dashboard
   - Verify you're redirected back to the home page since you're not authenticated

## Expected Results

- The user should not need to manually enter their user ID at any point
- After login, organizations should be automatically loaded
- Logout should clear both organization context and authentication
- The flow should be smooth and intuitive with proper loading states
