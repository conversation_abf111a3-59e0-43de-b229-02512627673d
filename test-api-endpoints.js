// Test script to check API endpoints
const API_BASE = 'http://localhost:4030/api/v1';

async function testEndpoint(endpoint, method = 'GET', headers = {}, body = null) {
  console.log(`\n🔍 Testing ${method} ${API_BASE}${endpoint}`);
  console.log('Headers:', headers);
  
  try {
    const response = await fetch(`${API_BASE}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      body: body ? JSON.stringify(body) : null
    });
    
    console.log(`✅ Status: ${response.status} ${response.statusText}`);
    console.log('Response Headers:', Object.fromEntries(response.headers.entries()));
    
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('Response Data:', data);
      return { status: response.status, data };
    } else {
      const text = await response.text();
      console.log('Response Text:', text);
      return { status: response.status, data: text };
    }
  } catch (error) {
    console.error(`❌ Error:`, error.message);
    return { status: 0, error: error.message };
  }
}

async function runTests() {
  console.log('🚀 Starting API endpoint tests...\n');
  
  // Test 1: Check if API is running
  await testEndpoint('/health');
  
  // Test 2: List organizations (should be public)
  await testEndpoint('/organization/');
  
  // Test 3: Try with organization headers
  const testOrgId = '1';
  const testPin = '1234';
  
  const orgHeaders = {
    'X-Organization-ID': testOrgId,
    'X-Organization-PIN': testPin
  };
  
  await testEndpoint('/shipment/', 'GET', orgHeaders);
  await testEndpoint('/status/all', 'GET', orgHeaders);
  await testEndpoint('/user/', 'GET', orgHeaders);
  
  // Test 4: Try without PIN
  const orgHeadersNoPIN = {
    'X-Organization-ID': testOrgId
  };
  
  await testEndpoint('/shipment/', 'GET', orgHeadersNoPIN);
  await testEndpoint('/user/', 'GET', orgHeadersNoPIN);
}

runTests().catch(console.error);
