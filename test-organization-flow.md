# Organization Flow Test

## API Flow

1. **Fetch All Organizations**:
   ```
   curl http://localhost:8000/api/v1/organization/
   ```
   This returns all organizations without needing a user ID.

2. **Get Specific Organization Details**:
   ```
   curl -H "X-Organization-ID: 1" http://localhost:8000/api/v1/organization/1
   ```
   Only organization ID header is needed for basic info.

## Application Flow

1. **Login**:
   - Login through the login dialog
   - You'll be redirected to the dashboard/organization selection

2. **Organization Selection**:
   - The application automatically fetches all available organizations
   - The first organization is pre-selected in the dropdown
   - Enter PIN for the selected organization
   - Click "Continue to Dashboard"

3. **Dashboard**:
   - Verify you can access dashboard with selected organization context
   - The organization switcher should show the selected organization name
   - All API calls should include proper organization headers

## Expected Results

- No need to fetch organizations by user ID
- Organizations load automatically
- The first organization is pre-selected
- After entering a PIN, the dashboard loads with the proper organization context
