#!/usr/bin/env node

/**
 * Test script for new tracking API endpoints integration
 * Tests the frontend implementation of the new tracking APIs
 */

const API_BASE = 'http://localhost:4030/api/v1';
const TEST_ORG_ID = '1';
const TEST_ORG_PIN = '1234';

async function testEndpoint(endpoint, method = 'GET', headers = {}, body = null) {
  const url = `${API_BASE}${endpoint}`;
  
  try {
    console.log(`\n🧪 Testing: ${method} ${url}`);
    console.log(`📤 Headers:`, headers);
    
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      }
    };
    
    if (body && method !== 'GET') {
      options.body = JSON.stringify(body);
      console.log(`📦 Body:`, body);
    }
    
    const response = await fetch(url, options);
    console.log(`📥 Response: ${response.status} ${response.statusText}`);
    
    let data;
    try {
      data = await response.json();
    } catch (e) {
      data = await response.text();
    }
    
    if (response.ok) {
      console.log(`✅ Success:`, typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
      return { success: true, status: response.status, data };
    } else {
      console.log(`❌ Error:`, typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
      return { success: false, status: response.status, error: data };
    }
  } catch (error) {
    console.log(`💥 Network Error:`, error.message);
    return { success: false, error: error.message };
  }
}

async function main() {
  console.log('🚀 TESTING NEW TRACKING API ENDPOINTS INTEGRATION');
  console.log('==================================================');
  
  const authHeaders = {
    'X-Organization-ID': TEST_ORG_ID,
    'X-Organization-PIN': TEST_ORG_PIN
  };
  
  // Test 1: Direct tracking by number (new endpoint)
  console.log('\n📋 TEST 1: GET /tracking/{tracking_number}');
  const tracking1 = await testEndpoint('/tracking/TRK000001', 'GET', authHeaders);
  
  // Test 2: Tracking by brokerage ID (enhanced endpoint)  
  console.log('\n📋 TEST 2: GET /status/{brokerage_id}/tracking');
  const tracking2 = await testEndpoint('/status/1/tracking', 'GET', authHeaders);
  
  // Test 3: Add tracking event by tracking number (new endpoint)
  console.log('\n📋 TEST 3: POST /tracking/?tracking_number=TRK000001');
  const newEvent = {
    status_description: "Frontend API Test Event",
    location: "Test Location - API Integration",
    notes: "Testing new tracking API endpoint from frontend"
  };
  const tracking3 = await testEndpoint('/tracking/?tracking_number=TRK000001', 'POST', authHeaders, newEvent);
  
  // Test 4: Add tracking event by brokerage ID (enhanced endpoint)
  console.log('\n📋 TEST 4: POST /status/1/tracking');
  const newEvent2 = {
    status_description: "Frontend Brokerage Test Event", 
    location: "Test Location - Brokerage API",
    notes: "Testing brokerage tracking API endpoint from frontend"
  };
  const tracking4 = await testEndpoint('/status/1/tracking', 'POST', authHeaders, newEvent2);
  
  // Test 5: Verify existing endpoints still work
  console.log('\n📋 TEST 5: GET /status/search (existing endpoint for comparison)');
  const existing = await testEndpoint('/status/search', 'GET', authHeaders);
  
  // Summary
  console.log('\n📊 TEST SUMMARY');
  console.log('===============');
  console.log(`1. Direct tracking by number: ${tracking1.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`2. Tracking by brokerage ID: ${tracking2.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`3. Add event by tracking number: ${tracking3.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`4. Add event by brokerage ID: ${tracking4.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`5. Existing endpoint (reference): ${existing.success ? '✅ PASS' : '❌ FAIL'}`);
  
  const passCount = [tracking1, tracking2, tracking3, tracking4, existing].filter(t => t.success).length;
  console.log(`\n🎯 OVERALL: ${passCount}/5 tests passed`);
  
  if (passCount === 5) {
    console.log('🎉 All new tracking API endpoints are working correctly!');
  } else if (passCount >= 1) {
    console.log('⚠️  Some endpoints are working, backend may need updates for full functionality');
  } else {
    console.log('🔧 New endpoints not yet available - frontend ready for when backend is updated');
  }
}

main().catch(console.error);
