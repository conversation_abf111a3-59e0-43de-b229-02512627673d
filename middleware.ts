import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Define routes that require authentication
const protectedRoutes = ['/dashboard'];

// Define routes that should redirect authenticated users
const authRoutes = ['/login'];

// Define public routes that don't need authentication checks
const publicRoutes = ['/', '/contact', '/privacy', '/terms', '/support'];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Get authentication token from cookies or headers
  const token = request.cookies.get('pace-token')?.value;
  
  // Check if user is authenticated by verifying token exists and is not expired
  const isAuthenticated = (() => {
    if (!token) return false;
    
    try {
      const parsedToken = JSON.parse(token);
      const now = Date.now();
      const tokenExpiry = parsedToken.expires_in * 1000; // Convert seconds to milliseconds
      
      // Basic expiry check - token should not be expired
      return now < tokenExpiry;
    } catch {
      return false;
    }
  })();
  
  // Handle protected routes
  if (protectedRoutes.some(route => pathname.startsWith(route))) {
    if (!isAuthenticated) {
      // Redirect to login if trying to access protected route without auth
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('redirect', pathname);
      return NextResponse.redirect(loginUrl);
    }
  }
  
  // Handle auth routes (login, register, etc.)
  if (authRoutes.some(route => pathname.startsWith(route))) {
    if (isAuthenticated) {
      // Redirect to dashboard if already authenticated and trying to access auth pages
      const dashboardUrl = new URL('/dashboard', request.url);
      return NextResponse.redirect(dashboardUrl);
    }
  }
  
  // Allow public routes and other paths to proceed
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - assets (public assets)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|assets).*)',
  ],
};