#!/usr/bin/env node

/**
 * Step-by-Step API Test for Fr8+ Dashboard
 * 
 * This script tests the exact API flow that the dashboard should follow:
 * 1. Test health endpoint
 * 2. Fetch organizations (public endpoint)
 * 3. Test organization-specific endpoints with ID and PIN
 * 4. Test shipment endpoints (/status/all and /status/search)
 */

const BASE_URL = 'http://localhost:4030/api/v1';

// Test organization credentials
const TEST_ORG_ID = '17'; // Updated to match user's actual organization ID
const TEST_ORG_PIN = '1234'; // Updated to match user's actual PIN

/**
 * Make an API request with proper error handling
 */
async function makeRequest(endpoint, options = {}) {
  const url = `${BASE_URL}${endpoint}`;
  
  const config = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers
    },
    ...options
  };
  
  console.log(`\n🔍 Testing: ${config.method} ${url}`);
  console.log(`📨 Headers:`, Object.keys(config.headers).join(', '));
  
  try {
    const response = await fetch(url, config);
    const contentType = response.headers.get('content-type');
    
    let data = null;
    try {
      if (contentType && contentType.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text();
      }
    } catch (e) {
      data = 'Unable to parse response';
    }
    
    console.log(`📥 Status: ${response.status} ${response.statusText}`);
    
    if (response.ok) {
      console.log(`✅ SUCCESS`);
      if (data && typeof data === 'object') {
        if (Array.isArray(data)) {
          console.log(`📊 Response: Array with ${data.length} items`);
          if (data.length > 0) {
            console.log(`📋 First item keys:`, Object.keys(data[0]).join(', '));
          }
        } else {
          console.log(`📊 Response:`, JSON.stringify(data, null, 2));
        }
      } else {
        console.log(`📊 Response:`, data);
      }
    } else {
      console.log(`❌ FAILED`);
      if (data) {
        console.log(`🚨 Error:`, typeof data === 'object' ? JSON.stringify(data, null, 2) : data);
      }
    }
    
    return {
      success: response.ok,
      status: response.status,
      data,
      url,
      method: config.method
    };
  } catch (error) {
    console.log(`💥 Network Error: ${error.message}`);
    return {
      success: false,
      status: 0,
      error: error.message,
      url,
      method: config.method
    };
  }
}

/**
 * Main test sequence
 */
async function runTests() {
  console.log('🧪 FR8+ API STEP-BY-STEP TEST');
  console.log('==============================');
  console.log(`Base URL: ${BASE_URL}`);
  console.log(`Test Org ID: ${TEST_ORG_ID}`);
  console.log(`Test Org PIN: ${TEST_ORG_PIN.substring(0, 2)}***`);
  
  const results = [];
  
  // Step 1: Test health endpoint
  console.log('\n📋 STEP 1: HEALTH CHECK');
  const healthResult = await makeRequest('/health/');
  results.push({ step: 'health', ...healthResult });
  
  if (!healthResult.success) {
    console.log('⚠️ Health check failed, but continuing with tests...');
  }
  
  // Step 2: Test organizations endpoint (public, no auth)
  console.log('\n📋 STEP 2: FETCH ORGANIZATIONS (PUBLIC)');
  const orgResult = await makeRequest('/organization/');
  results.push({ step: 'organizations_public', ...orgResult });
  
  if (orgResult.success && Array.isArray(orgResult.data)) {
    console.log(`🏢 Found ${orgResult.data.length} organizations`);
    if (orgResult.data.length > 0) {
      const org = orgResult.data.find(o => o.id?.toString() === TEST_ORG_ID);
      if (org) {
        console.log(`✅ Found our test organization: ${org.name || org.id}`);
      } else {
        console.log(`⚠️ Test organization ID ${TEST_ORG_ID} not found in list`);
        console.log(`Available IDs: ${orgResult.data.map(o => o.id).join(', ')}`);
      }
    }
  }
  
  // Step 3: Test organization endpoint with auth headers
  console.log('\n📋 STEP 3: FETCH ORGANIZATIONS (WITH AUTH)');
  const authHeaders = {
    'X-Organization-ID': TEST_ORG_ID,
    'X-Organization-PIN': TEST_ORG_PIN
  };
  
  const orgAuthResult = await makeRequest('/organization/', { headers: authHeaders });
  results.push({ step: 'organizations_auth', ...orgAuthResult });
  
  // Step 4: Test shipments endpoint - /status/all (requires auth)
  console.log('\n📋 STEP 4: FETCH ALL SHIPMENTS (/status/all)');
  const shipmentsResult = await makeRequest('/status/all', { headers: authHeaders });
  results.push({ step: 'shipments_all', ...shipmentsResult });
  
  if (shipmentsResult.success) {
    console.log(`🚢 Successfully fetched shipments!`);
    if (Array.isArray(shipmentsResult.data)) {
      console.log(`📦 Found ${shipmentsResult.data.length} shipments`);
      if (shipmentsResult.data.length > 0) {
        const firstShipment = shipmentsResult.data[0];
        console.log(`📋 Sample shipment fields:`, Object.keys(firstShipment).join(', '));
      }
    }
  } else {
    console.log(`❌ Shipments fetch failed with status ${shipmentsResult.status}`);
    if (shipmentsResult.status === 422) {
      console.log('💡 This might be due to missing or invalid headers');
    } else if (shipmentsResult.status === 403) {
      console.log('💡 This might be due to incorrect PIN or unauthorized access');
    } else if (shipmentsResult.status === 404) {
      console.log('💡 This might be due to incorrect endpoint path');
    }
  }
  
  // Step 5: Test shipments search endpoint - /status/search (requires auth)
  console.log('\n📋 STEP 5: SEARCH SHIPMENTS (/status/search)');
  const searchResult = await makeRequest('/status/search', { headers: authHeaders });
  results.push({ step: 'shipments_search', ...searchResult });
  
  // Step 6: Test users endpoint (with auth)
  console.log('\n📋 STEP 6: FETCH USERS (WITH AUTH)');
  const usersResult = await makeRequest('/user/', { headers: authHeaders });
  results.push({ step: 'users_auth', ...usersResult });
  
  // Step 7: Test users endpoint (without auth)
  console.log('\n📋 STEP 7: FETCH USERS (NO AUTH)');
  const usersNoAuthResult = await makeRequest('/user/');
  results.push({ step: 'users_no_auth', ...usersNoAuthResult });
  
  // Summary
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('=======================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful: ${successful.length}/${results.length}`);
  console.log(`❌ Failed: ${failed.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n✅ WORKING ENDPOINTS:');
    successful.forEach(result => {
      console.log(`  ${result.step}: ${result.method} ${result.url} → ${result.status}`);
    });
  }
  
  if (failed.length > 0) {
    console.log('\n❌ FAILED ENDPOINTS:');
    failed.forEach(result => {
      console.log(`  ${result.step}: ${result.method} ${result.url} → ${result.status || 'Network Error'}`);
    });
  }
  
  // Key findings
  console.log('\n🔍 KEY FINDINGS:');
  
  const shipmentEndpoint = results.find(r => r.step === 'shipments_all');
  if (shipmentEndpoint?.success) {
    console.log('✅ Shipment endpoint (/status/all) is working correctly');
    console.log('✅ Organization ID and PIN headers are being accepted');
  } else {
    console.log('❌ Shipment endpoint (/status/all) is not working');
    console.log(`   Status: ${shipmentEndpoint?.status || 'Unknown'}`);
    console.log('💡 Check organization ID, PIN, or endpoint implementation');
  }
  
  const orgEndpoint = results.find(r => r.step === 'organizations_public');
  if (orgEndpoint?.success) {
    console.log('✅ Organization listing endpoint is working');
  } else {
    console.log('❌ Organization listing endpoint is not working');
  }
  
  // Save results
  const fs = require('fs');
  const detailedResults = {
    timestamp: new Date().toISOString(),
    baseUrl: BASE_URL,
    testOrgId: TEST_ORG_ID,
    summary: {
      total: results.length,
      successful: successful.length,
      failed: failed.length
    },
    results
  };
  
  fs.writeFileSync('step-by-step-test-results.json', JSON.stringify(detailedResults, null, 2));
  console.log('\n💾 Detailed results saved to step-by-step-test-results.json');
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (shipmentEndpoint?.success) {
    console.log('✅ Your dashboard should work with /status/all endpoint');
    console.log('✅ Use Organization ID: 17 and PIN: 1234');
  } else {
    console.log('❌ Dashboard needs debugging:');
    console.log('   1. Verify organization ID and PIN are correct');
    console.log('   2. Check that headers are being sent properly');
    console.log('   3. Verify API server is running and accessible');
  }
}

// Run the tests
runTests().catch(console.error);