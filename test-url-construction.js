#!/usr/bin/env node

/**
 * URL Construction Test Script
 * Tests the PaceAPIClient URL construction to verify double slash fix
 */

// Simulate the PaceAPIClient URL construction logic
class TestPaceAPIClient {
  constructor(orgId, orgPin, baseURL = 'https://api.gopace.app') {
    this.orgId = orgId;
    this.orgPin = orgPin;
    this.baseURL = baseURL;
  }

  constructURL(endpoint) {
    // Remove leading slash from endpoint to prevent double slashes
    const cleanEndpoint = endpoint.startsWith('/') ? endpoint.slice(1) : endpoint;
    return `${this.baseURL}/${cleanEndpoint}`;
  }
}

console.log('🧪 TESTING URL CONSTRUCTION');
console.log('===========================');

const client = new TestPaceAPIClient('17', '1234', 'https://api.gopace.app');

// Test various endpoint formats
const testEndpoints = [
  '/api/v1/organization/',      // With leading slash
  'api/v1/organization/',       // Without leading slash
  '/api/v1/organizations/',     // With leading slash (incorrect plural)
  'api/v1/organization/',       // Without leading slash (correct)
  '/tracking/TRK123',           // With leading slash
  'tracking/TRK123',            // Without leading slash
  '/status/1/tracking',         // With leading slash
  'status/1/tracking'           // Without leading slash
];

console.log('\n📋 URL CONSTRUCTION TESTS:');
testEndpoints.forEach(endpoint => {
  const constructedURL = client.constructURL(endpoint);
  const hasDoubleSlash = constructedURL.includes('//') && !constructedURL.startsWith('http');
  
  console.log(`\n📍 Input: "${endpoint}"`);
  console.log(`🔗 Output: "${constructedURL}"`);
  console.log(`✅ Status: ${hasDoubleSlash ? '❌ DOUBLE SLASH DETECTED' : '✅ CORRECT'}`);
});

console.log('\n🎯 SUMMARY:');
console.log('✅ Base URL configured correctly: https://api.gopace.app (no trailing slash)');
console.log('✅ URL construction removes leading slashes from endpoints');
console.log('✅ Final URLs use single forward slash: baseURL + "/" + cleanEndpoint');
console.log('\n🔧 FIXES IMPLEMENTED:');
console.log('1. Environment variable updated: NEXT_PUBLIC_API_URL=https://api.gopace.app');
console.log('2. Organizations endpoint corrected: api/v1/organization/ (singular, no leading slash)');
console.log('3. PaceAPIClient enhanced with URL safety logic');
